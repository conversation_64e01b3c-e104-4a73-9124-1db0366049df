// Dart imports:
import 'dart:async';
import 'dart:convert';

// Package imports:
import 'package:ez_intl/ez_intl.dart';
import 'package:native_shared_preferences/native_shared_preferences.dart';

// Project imports:
import '../../../../../domain/entities/entities.dart';
import '../../../../../injector/injector.dart';
import '../../../../models/chat_search_item.dart';
import '../../../../models/nd_models.dart';
import '../../../ez_datasources.dart';
import '../../assign_task/assign_task_dao.dart';
import '../../branch_chat_list/branch_chat_list_dao.dart';
import '../../branch_selection/branch_selection_dao.dart';
import '../../chat/chat_dao.dart';
import '../../chat_list/chat_list_dao.dart';
import '../../chat_select_branch/chat_select_branch_dao.dart';
import '../../checkin_photo/checkin_photo_dao.dart';
import '../../consultation_customer/consultation_customer_dao.dart';
import '../../consultation_manager/consultation_manager_dao.dart';
import '../../create_chat_folder/create_chat_folder_dao.dart';
import '../../create_chat_group/create_chat_group_dao.dart';
import '../../create_customer/create_customer_dao.dart';
import '../../customer_booking_info/customer_booking_info_dao.dart';
import '../../customer_info_details/customer_info_details_dao.dart';
import '../../customer_list/customer_list_dao.dart';
import '../../customer_profile/customer_profile_dao.dart';
import '../../customer_record/customer_record_dao.dart';
import '../../customer_schedule/customer_schedule_dao.dart';
import '../../detail_crm_customer/detail_crm_customer_dao.dart';
import '../../detail_staff_evaluation_period/detail_staff_evaluation_period_dao.dart';
import '../../dev/dev_dao.dart';
import '../../feedback/feedback_dao.dart';
import '../../group_chat_detail/group_chat_detail_dao.dart';
import '../../hr_organization/hr_organization_dao.dart';
import '../../important_notes/important_notes_dao.dart';
import '../../list_customer/list_customer_dao.dart';
import '../../medical_department_list/medical_department_list_dao.dart';
import '../../medical_log_detail/medical_log_detail_dao.dart';
import '../../medical_product_creation/medical_product_creation_dao.dart';
import '../../medical_service_creation/medical_service_creation_dao.dart';
import '../../medical_service_list/medical_service_list_dao.dart';
import '../../medical_service_log_list/medical_service_log_list_dao.dart';
import '../../medical_template_list/medical_template_list_dao.dart';
import '../../medicine_detail/medicine_detail_dao.dart';
import '../../note_details/note_details_dao.dart';
import '../../notification_list/notification_list_dao.dart';
import '../../px_list/px_list_dao.dart';
import '../../px_recheck/px_recheck_dao.dart';
import '../../px_task_list/px_task_list_dao.dart';
import '../../px_unasigned/px_unasigned_dao.dart';
import '../../px_unasigned_update/px_unasigned_update_dao.dart';
import '../../schedule_details/schedule_details_dao.dart';
import '../../select_px_room/select_px_room_dao.dart';
import '../../service_and_product/service_and_product_dao.dart';
import '../../staff_evaluation_periods/staff_evaluation_periods_dao.dart';
import '../../story_detail/story_detail_dao.dart';
import '../../tag_list/tag_list_dao.dart';
import '../../taking_care_customer/taking_care_customer_dao.dart';
import '../../ticket_detail/ticket_detail_dao.dart';
import '../../user_list/user_list_dao.dart';

class EZCache {
  EZCache._internal(this.box);

  static final EZCache shared = EZCache._internal(
    Hive.box<dynamic>(CollaboratorKeys.hiveBoxName),
  );

  final Box<dynamic> box;

  String? get accessToken {
    return box
            .get(CollaboratorKeys.accessToken, defaultValue: '')
            ?.toString() ??
        '';
  }

  String? get accessTokenSocial {
    return box
            .get(CollaboratorKeys.accessTokenSocial, defaultValue: '')
            ?.toString() ??
        '';
  }

  static Future<String?> get accessTokenPref async {
    final prefs = await NativeSharedPreferences.getInstance();
    final token = prefs.getString(CollaboratorKeys.accessToken);

    return token;
  }

  Future<void> saveChatList(
    final String key, // userName + folderId
    final List<ChatListItems?>? value,
  ) async {
    return box.put(key + Keys.chatList, value);
  }

  List<ChatListItems?> chatList(
    // userName + folderId
    final String key,
  ) {
    try {
      final chatList =
          (box.get(key + Keys.chatList) as List<dynamic>?) ??
          <ChatListItems?>[];

      return chatList.map((final dynamic e) => e as ChatListItems?).toList();
    } catch (_) {
      return [];
    }
  }

  Future<void> saveFolderList(
    final String key, // userName
    final CreateChatFolderLoad? value,
  ) async {
    return box.put(key + Keys.folderList, value);
  }

  CreateChatFolderLoad? folderList(
    final String key, // userName
  ) {
    try {
      return box.get(key + Keys.folderList) as CreateChatFolderLoad?;
    } catch (_) {
      return null;
    }
  }

  Future<void> saveRecentContactList(final List<ChatSearchItem?>? value) async {
    final user = getUserProfile();
    return box.put((user?.employeeId ?? '') + Keys.recentContactList, value);
  }

  List<ChatSearchItem?> recentContactList() {
    try {
      final user = getUserProfile();

      final chatList =
          (box.get((user?.employeeId ?? '') + Keys.recentContactList)
              as List<dynamic>?) ??
          <ChatSearchItem?>[];

      return chatList.map((final dynamic e) => e as ChatSearchItem?).toList();
    } catch (_) {
      return [];
    }
  }

  Future<void> saveDraftMessage(
    final String conversationId,
    final String? draftMessage,
  ) async {
    final user = getUserProfile();
    return box.put(
      (user?.employeeId ?? '') + conversationId + Keys.draftMessage,
      draftMessage,
    );
  }

  String getDraftMessage(final String conversationId) {
    final user = getUserProfile();
    return box
            .get(
              (user?.employeeId ?? '') + conversationId + Keys.draftMessage,
              defaultValue: '',
            )
            ?.toString() ??
        '';
  }

  Future<void> saveLastSeenMessage(
    final String conversationId,
    final String? createdAt,
  ) async {
    final user = getUserProfile();
    return box.put(
      (user?.employeeId ?? '') + conversationId + Keys.lastSeenMessage,
      createdAt,
    );
  }

  String getLastSeenMessage(final String conversationId) {
    final user = getUserProfile();
    return box
            .get(
              (user?.employeeId ?? '') + conversationId + Keys.lastSeenMessage,
              defaultValue: '',
            )
            ?.toString() ??
        '';
  }

  Future<void> saveCheckinRemaider(final List<bool> isSwitched) async {
    return box.put(CollaboratorKeys.isSwitched, isSwitched);
  }

  List<bool> get checkinRemaider {
    final isSwitched = [true, true, true, true, true, true, true];

    return box.get(CollaboratorKeys.isSwitched, defaultValue: isSwitched)
        as List<bool>;
  }

  Future<void> saveCheckinHour(final BasicHour checkinHour) async {
    return box.put(CollaboratorKeys.checkinHour, checkinHour);
  }

  Future<void> saveStoryList(final List<String> idList) async {
    return box.put(CollaboratorKeys.storyList, idList);
  }

  Future<void> saveHomeItem(
    final String key,
    final List<ServiceItemsModel?> items,
  ) async {
    try {
      await box.put(key, items);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> saveStickerRecent(
    final List<String> stickers, [
    final int limit = 19,
  ]) {
    final user = getUserProfile();
    final stickersSave = stickers.reversed.toList().sublist(
      0,
      stickers.length > limit ? limit : null,
    );
    return box.put(
      'sticker${user?.employeeId}',
      stickersSave.reversed.toList(),
    );
  }

  Future<void> saveHistoryRecent(
    final List<String> keySearchs, [
    final int limit = 19,
  ]) {
    final user = getUserProfile();
    final stickersSave = keySearchs.toList().sublist(
      0,
      keySearchs.length > limit ? limit : null,
    );
    return box.put('history${user?.employeeId}', stickersSave.toList());
  }

  Future<List<String>> getHistoryRecent() async {
    final user = getUserProfile();
    final listStr = await get<List<String>>('history${user?.employeeId}') ?? [];

    // final listMap = listStr.map((final e) => json.decode(e)).toList();
    return listStr;
  }

  Future<List<ServiceItemsModel?>> getHomeItem(final String key) async {
    try {
      // Check if key exists in box
      final hasKey = box.containsKey(key);

      if (!hasKey) {
        return <ServiceItemsModel?>[];
      }

      // Get raw data directly from box
      final rawData = box.get(key);

      if (rawData == null) {
        return <ServiceItemsModel?>[];
      }

      if (rawData is List) {
        final result = rawData
            .map((final dynamic e) => e as ServiceItemsModel?)
            .toList();
        return result;
      }
      return <ServiceItemsModel?>[];
    } catch (e) {
      return [];
    }
  }

  Future<List<ChatGetUserStickerItemsStickersModel>> getStickerRecent() async {
    final user = getUserProfile();
    final listStr = await get<List<String>>('sticker${user?.employeeId}') ?? [];

    final listMap = listStr.map((final e) => json.decode(e)).toList();
    return listMap
        .map(
          (final jsonE) =>
              ChatGetUserStickerItemsStickersModel?.fromJson(jsonE),
        )
        .toList();
  }

  Future<List<String>?> get storyList {
    return get(CollaboratorKeys.storyList);
  }

  Future<BasicHour> get checkinHour async {
    return await box.get(
      CollaboratorKeys.checkinHour,
      defaultValue: BasicHour(hour: 8, minute: 0),
    );
  }

  Future<void> saveCheckoutHour(final BasicHour checkinHour) async {
    return box.put(CollaboratorKeys.checkoutHour, checkinHour);
  }

  Future<BasicHour> get checkoutHour async {
    return await box.get(
      CollaboratorKeys.checkoutHour,
      defaultValue: BasicHour(hour: 17, minute: 0),
    );
  }

  // General Methods: ----------------------------------------------------------
  Future<T?> get<T>(final String key) async {
    return await box.get(key) as T?;
  }

  Future<void> removeAccessToken() async {
    final prefs = await NativeSharedPreferences.getInstance();
    await prefs.remove(CollaboratorKeys.accessToken);
    return box.delete(CollaboratorKeys.accessToken);
  }

  Future<void> removeAccountLoyalty() async {
    return box.delete(CollaboratorKeys.accountModel);
  }

  Future<void> removeUserProfile() async {
    return box.delete(CollaboratorKeys.userProfile);
  }

  UserModel? getUserProfile() {
    return box.get(CollaboratorKeys.userProfile) as UserModel?;
  }

  Future<void> save(final String key, final dynamic value) async {
    return box.put(key, value);
  }

  Future<void> remove(final String key) async {
    return box.delete(key);
  }

  Future<void> saveAccessToken(final String? authToken) async {
    final prefs = await NativeSharedPreferences.getInstance();
    await prefs.setString(CollaboratorKeys.accessToken, authToken);
    return box.put(CollaboratorKeys.accessToken, authToken);
  }

  Future<void> saveAccessTokenSocial(final String? authToken) async {
    return box.put(CollaboratorKeys.accessTokenSocial, authToken);
  }

  Future<void> saveSocketAccessToken(final String? socketAccessToken) async {
    return box.put(Keys.socketAccessToken, socketAccessToken);
  }

  Future<void> saveChatLoginData(
    final LoginSocketAccessTokenGet? chatLoginData,
  ) async {
    return box.put(Keys.chatLoginData, chatLoginData);
  }

  LoginSocketAccessTokenGet? get chatLoginData {
    return box.get(Keys.chatLoginData);
  }

  String? get socketAccessToken {
    return box.get(Keys.socketAccessToken, defaultValue: '')?.toString() ?? '';
  }

  Future<void> saveFirstLogin({required final bool isFirstLogin}) async {
    return box.put(CollaboratorKeys.isFirstLogin, isFirstLogin);
  }

  Future<void> saveLoyaltyInfo(final UserInfoModel userInfoModel) async {
    return box.put(CollaboratorKeys.accountModel, userInfoModel);
  }

  Future<void> saveUserProfile(final UserModel? user) async {
    return box.put(CollaboratorKeys.userProfile, user);
  }

  Future<void> saveCrmUserInfo(final UserInfo? crmUserInfo) async {
    return box.put(CollaboratorKeys.crmUserInfo, crmUserInfo);
  }

  Future<UserInfo?> get crmUserInfo async {
    return box.get(CollaboratorKeys.crmUserInfo) as UserInfo?;
  }

  Future<void> saveConfigurations(
    final ConfigurationsModel? configurations,
  ) async {
    return box.put(CollaboratorKeys.configurations, configurations);
  }

  Future<void> saveServices(final List<ServiceItemsModel?> services) async {
    await box.put(CollaboratorKeys.collaboratorServices, services);
  }

  Future<void> saveCheckListHomeMenu(final Map<String, bool> services) {
    return box.put(CollaboratorKeys.checkListHomeMenu, services);
  }

  Future<void> removeHomeMenuCache() async {
    return box.delete(CollaboratorKeys.checkListHomeMenu);
  }

  Future<Map<String, bool>> get checkListHomeMenu async {
    try {
      final checkListMap =
          await EZCache.shared.get<Map<String, dynamic>?>(
            CollaboratorKeys.checkListHomeMenu,
          ) ??
          <String, dynamic>{};

      return Map<String, bool>.from(checkListMap);
    } catch (_) {
      return {};
    }
  }

  Future<List<ServiceItemsModel?>> get collaboratorServices async {
    try {
      final collaboratorServices =
          await EZCache.shared.get<List<ServiceItemsModel?>?>(
            CollaboratorKeys.collaboratorServices,
          ) ??
          <ServiceItemsModel?>[];

      return collaboratorServices
          .map((final dynamic e) => e as ServiceItemsModel?)
          .toList();
    } catch (_) {
      return [];
    }
  }

  Future<ConfigurationsModel?> get configurations {
    return EZCache.shared.get<ConfigurationsModel?>(
      CollaboratorKeys.configurations,
    );
  }

  Future<String?> get contactUrl async {
    final configs = await configurations;

    return configs?.collaborator?.contactUrl;
  }

  Future<int?> get currentDay async {
    final configs = await configurations;

    return configs?.currentDate?.day;
  }

  Future<int?> get currentMonth async {
    final configs = await configurations;

    return configs?.currentDate?.month;
  }

  Future<int?> get currentYear async {
    final configs = await configurations;

    return configs?.currentDate?.year;
  }

  Future<String?> get hotline async {
    final configs = await configurations;

    return configs?.hotline;
  }

  Future<String> get registerCollaboratorUrl async {
    final configs = await configurations;

    return configs?.collaborator?.registerUrl ?? '';
  }

  Future<String?> get collaboratorMenus async {
    final configs = await configurations;

    return configs?.ctvMenu;
  }

  String? get phone {
    return getUserProfile()?.phone;
  }

  Future<List<AccountMenusModel?>> get accountMenus async {
    final configs = await configurations;

    return configs?.accountMenus ?? [];
  }

  Future<List<SupportItemModel?>> get supports async {
    final configs = await configurations;

    return configs?.support ?? [];
  }

  Future<String?> get termsUrl async {
    final configs = await configurations;

    return configs?.termsUrl;
  }

  Future<EventsModel?> get events async {
    final configs = await configurations;

    return configs?.events;
  }

  Future<String?> get titleScheduler async {
    final configs = await configurations;

    return configs?.appMessage?.appMessageData?.title;
  }

  Future<String?> get messageScheduler async {
    final configs = await configurations;

    return configs?.appMessage?.appMessageData?.messsage;
  }

  Future<List<String?>> get externalLinks async {
    final configs = await configurations;

    return configs?.externalUrls ?? [];
  }

  Future<void> saveAppVersion(final String? appVersion) async {
    return box.put(Keys.versionApp, appVersion);
  }

  String get appVersion {
    return box.get(Keys.versionApp, defaultValue: '')?.toString() ?? '';
  }

  Future<void> saveDeviceInfo(final Map<String, dynamic> deviceInfo) async {
    return box.put(Keys.deviceInfo, deviceInfo);
  }

  Map<String, dynamic> get deviceInfo {
    return box.get(Keys.deviceInfo) as Map<String, dynamic>? ?? {};
  }

  String latitude({required final String defaultValue}) {
    return box.get(Keys.latitude, defaultValue: defaultValue)?.toString() ?? '';
  }

  String longitude({required final String defaultValue}) {
    return box.get(Keys.longitude, defaultValue: defaultValue)?.toString() ??
        '';
  }

  static Future<void> removeAuthToken() async {
    await EZCache.shared.box.delete(CollaboratorKeys.accessToken);
  }

  static Future<void> removeLoginModel() async {
    await EZCache.shared.box.delete(CollaboratorKeys.loginModel);
  }

  Future<String> get languageOption async {
    final option = await get<String?>(Keys.languageOption);

    return option ?? AppLocale.vi;
  }

  Future<void> saveLanguageOption(final String languageIndex) =>
      save(Keys.languageOption, languageIndex);

  Future<String> get customerRoomCode async {
    final roomCode = await get<String?>(Keys.customerRoomCode);

    return roomCode ?? '';
  }

  Future<void> saveCustomerRoomCode(final String? roomCode) =>
      save(Keys.customerRoomCode, roomCode);

  //this is method check dev mode
  Future<bool> get enableOnlineLogger async {
    final enable = await get<bool?>(Keys.enableOnlineLogger);

    return enable ?? false;
  }

  Future<String?> get loginStringee async {
    final prefs = await NativeSharedPreferences.getInstance();
    final stringeeToken = prefs.get(CollaboratorKeys.loginStringee);
    return stringeeToken.toString();
  }

  Future<void> saveLoginStringee(final String? stringeeData) async {
    final prefs = await NativeSharedPreferences.getInstance();
    await prefs.setString(CollaboratorKeys.loginStringee, stringeeData);
  }

  Future<UserPermission?> get userPermission async {
    return box.get(Keys.userPermission) as UserPermission?;
  }

  Future<void> saveUserPermission(final UserPermission? userPermission) async {
    return box.put(Keys.userPermission, userPermission);
  }

  bool get accessGlobalDomain {
    return box.get(Keys.accessGlobalDomain, defaultValue: false) as bool;
  }

  Future<void> saveAccessGlobalDomain({
    required final bool? accessGlobalDomain,
  }) async {
    return box.put(Keys.accessGlobalDomain, accessGlobalDomain);
  }

  String? get environment {
    return box.get(Keys.environment, defaultValue: null)?.toString();
  }

  Future<void> saveEnvironment({required final String? environment}) async {
    return box.put(Keys.environment, environment);
  }

  bool get globalDomainMode {
    return box.get(Keys.globalDomainMode, defaultValue: false) as bool;
  }

  Future<void> saveGlobalDomainMode({
    required final bool? globalDomainMode,
  }) async {
    return box.put(Keys.globalDomainMode, globalDomainMode);
  }

  Future<void> removeLoginStringee() async {
    final prefs = await NativeSharedPreferences.getInstance();
    await prefs.remove(CollaboratorKeys.loginStringee);
  }

  Future<double> get textScale async {
    final textScale = await get(Keys.textScale);
    return textScale ?? 1;
  }

  Future<void> saveTextScale(final double? textScale) async {
    return save(Keys.textScale, textScale);
  }

  Future<void> removeTextScale() async {
    return remove(Keys.textScale);
  }

  Future<String?> get quickAction async {
    final prefs = await NativeSharedPreferences.getInstance();
    final quickActionName = prefs.get(CollaboratorKeys.quickAction);
    return quickActionName.toString();
  }

  Future<void> saveQuickAction(final String? quickAction) async {
    final prefs = await NativeSharedPreferences.getInstance();
    await prefs.setString(CollaboratorKeys.quickAction, quickAction);
  }

  Future<void> removeQuickAction() async {
    final prefs = await NativeSharedPreferences.getInstance();
    await prefs.remove(CollaboratorKeys.quickAction);
  }

  //this is method save dev mode
  Future<void> saveEnableOnlineLogger({required final bool enable}) =>
      save(Keys.enableOnlineLogger, enable);

  static Future<void> storeValue(
    final String key,
    final ConfigurationsModel value,
  ) async {
    await EZCache.shared.box.put(key, value);
  }

  static List<DownloadedFileModel>? getFileList() {
    return (EZCache.shared.box.get(
              CollaboratorKeys.downloadedFile,
              defaultValue: <DownloadedFileModel>[],
            )
            as List)
        .map((final dynamic e) => e as DownloadedFileModel)
        .toList();
  }

  static Future<void> addFile(final DownloadedFileModel file) async {
    final list =
        (EZCache.shared.box.get(
                  CollaboratorKeys.downloadedFile,
                  defaultValue: <DownloadedFileModel>[],
                )
                as List)
            .map((final dynamic e) => e as DownloadedFileModel)
            .toList();
    list.add(file);
    EZCache.shared.box.put(CollaboratorKeys.downloadedFile, list);
  }

  static Future<void> removeFile(final DownloadedFileModel file) async {
    final list =
        (EZCache.shared.box.get(
                  CollaboratorKeys.downloadedFile,
                  defaultValue: <DownloadedFileModel>[],
                )
                as List)
            .map((final dynamic e) => e as DownloadedFileModel)
            .toList();
    list.removeWhere((final e) => e.taskId == file.taskId);
    EZCache.shared.box.put(CollaboratorKeys.downloadedFile, list);
  }

  /// cache initialization
  static Future<void> init() async {
    await Hive.initFlutter();
    await registerAdapter();
    await Hive.openBox<dynamic>(CollaboratorKeys.hiveBoxName);
  }

  static Future<void> registerAdapter() async {
    //register user object
    Hive.registerAdapter(UserModelAdapter());
    Hive.registerAdapter(UserChatModelAdapter());
    Hive.registerAdapter(BranchSelectionModelAdapter());
    Hive.registerAdapter(MedicalDepartmentListModelAdapter());
    Hive.registerAdapter(MedicalServiceListModelAdapter());
    Hive.registerAdapter(MedicalTemplateListModelAdapter());
    Hive.registerAdapter(UserCheckinModelAdapter());
    Hive.registerAdapter(UserInfoAdapter());
    Hive.registerAdapter(StringeeTokenAdapter());
    Hive.registerAdapter(BasicHourAdapter());
    Hive.registerAdapter(LoginStringeeAdapter());

    //register configuration
    Hive.registerAdapter(ServiceItemsModelAdapter());
    Hive.registerAdapter(DownloadedFileModelAdapter());
    Hive.registerAdapter(SupportItemModelAdapter());
    Hive.registerAdapter(ConfigurationsResponseModelAdapter());
    Hive.registerAdapter(ConfigurationsModelAdapter());
    Hive.registerAdapter(AccountMenusModelAdapter());
    Hive.registerAdapter(CurrentDateModelAdapter());
    Hive.registerAdapter(EventsModelAdapter());
    Hive.registerAdapter(AppMessageModelAdapter());
    Hive.registerAdapter(AppMessageDataModelAdapter());
    Hive.registerAdapter(PrintModelAdapter());

    //register navigation info
    Hive.registerAdapter(NavigationInfoResponseModelAdapter());
    Hive.registerAdapter(NavigationInfoModelAdapter());

    //register info loyalty
    Hive.registerAdapter(UserInfoResponseModelAdapter());
    Hive.registerAdapter(UserInfoModelAdapter());
    Hive.registerAdapter(RankingModelAdapter());
    Hive.registerAdapter(CycleRankingModelAdapter());
    Hive.registerAdapter(LoginSocketAccessTokenGetAdapter());
    Hive.registerAdapter(LoginSocketAccessTokenGetUserAdapter());
    Hive.registerAdapter(
      LoginSocketAccessTokenGetUserBotMessagePermissionAdapter(),
    );
    Hive.registerAdapter(RecordPermissionAdapter());
    Hive.registerAdapter(ChatPermissionAdapter());
    Hive.registerAdapter(UserPermissionAdapter());
    Hive.registerAdapter(ChatListItemsAdapter());
    Hive.registerAdapter(ChatListItemsMembersInfoAdapter());
    Hive.registerAdapter(ChatListItemsLastMessageInfoAdapter());
    Hive.registerAdapter(ChatListItemsConversationDetailsAdapter());
    Hive.registerAdapter(ChatItemsAdapter());
    Hive.registerAdapter(ChatItemsCreatedByInfoAdapter());
    Hive.registerAdapter(ChatItemsAttachmentAdapter());
    Hive.registerAdapter(CreateChatFolderLoadAdapter());
    Hive.registerAdapter(CreateChatFolderLoadDocsAdapter());
    Hive.registerAdapter(UserCheckPermissionPermissionAdapter());
    Hive.registerAdapter(ChatSearchItemAdapter());
    Hive.registerAdapter(MentionMessageAdapter());
    Hive.registerAdapter(SpeechToTextAdapter());
    Hive.registerAdapter(
      LoginSocketAccessTokenGetUserMessagePermissionAdapter(),
    );

    // Hive.registerAdapter(StoryDetailAdapter());
  }

  BranchSelectionDao get branchSelectionDao => getIt<BranchSelectionDao>();
  CustomerProfileDao get customerProfileDao => getIt<CustomerProfileDao>();
  CustomerInfoDetailsDao get customerInfoDetailsDao =>
      getIt<CustomerInfoDetailsDao>();
  ListCustomerDao get listCustomerDao => getIt<ListCustomerDao>();
  ImportantNotesDao get importantNotesDao => getIt<ImportantNotesDao>();
  MedicalDepartmentListDao get medicalDepartmentListDao =>
      getIt<MedicalDepartmentListDao>();
  MedicalServiceListDao get medicalServiceListDao =>
      getIt<MedicalServiceListDao>();
  NoteDetailsDao get noteDetailsDao => getIt<NoteDetailsDao>();
  MedicalServiceLogListDao get medicalServiceLogListDao =>
      getIt<MedicalServiceLogListDao>();
  MedicalLogDetailDao get medicalLogDetailDao => getIt<MedicalLogDetailDao>();
  MedicineDetailDao get medicineDetailDao => getIt<MedicineDetailDao>();
  ServiceAndProductDao get serviceAndProductDao =>
      getIt<ServiceAndProductDao>();
  MedicalTemplateListDao get medicalTemplateListDao =>
      getIt<MedicalTemplateListDao>();
  MedicalProductCreationDao get medicalProductCreationDao =>
      getIt<MedicalProductCreationDao>();
  MedicalServiceCreationDao get medicalServiceCreationDao =>
      getIt<MedicalServiceCreationDao>();
  ScheduleDetailsDao get scheduleDetailsDao => getIt<ScheduleDetailsDao>();
  CustomerScheduleDao get customerScheduleDao => getIt<CustomerScheduleDao>();
  CustomerBookingInfoDao get customerBookingInfoDao =>
      getIt<CustomerBookingInfoDao>();
  AssignTaskDao get assignTaskDao => getIt<AssignTaskDao>();
  ChatSelectBranchDao get chatSelectBranchDao => getIt<ChatSelectBranchDao>();
  BranchChatListDao get branchChatListDao => getIt<BranchChatListDao>();
  PxListDao get pxListDao => getIt<PxListDao>();
  PxUnasignedDao get pxUnasignedDao => getIt<PxUnasignedDao>();
  PxTaskListDao get pxTaskListDao => getIt<PxTaskListDao>();
  PxUnasignedUpdateDao get pxUnasignedUpdateDao =>
      getIt<PxUnasignedUpdateDao>();
  TakingCareCustomerDao get takingCareCustomerDao =>
      getIt<TakingCareCustomerDao>();
  PxRecheckDao get pxRecheckDao => getIt<PxRecheckDao>();
  CreateCustomerDao get createCustomerDao => getIt<CreateCustomerDao>();
  SelectPxRoomDao get selectPxRoomDao => getIt<SelectPxRoomDao>();
  CustomerListDao get customerListDao => getIt<CustomerListDao>();
  ConsultationManagerDao get consultationManagerDao =>
      getIt<ConsultationManagerDao>();

  ConsultationCustomerDao get consultationCustomerDao =>
      getIt<ConsultationCustomerDao>();
  StaffEvaluationPeriodsDao get staffEvaluationPeriodsDao =>
      getIt<StaffEvaluationPeriodsDao>();
  DetailStaffEvaluationPeriodDao get detailStaffEvaluationPeriodDao =>
      getIt<DetailStaffEvaluationPeriodDao>();
  DetailCrmCustomerDao get detailCrmCustomerDao =>
      getIt<DetailCrmCustomerDao>();
  HrOrganizationDao get hrOrganizationDao => getIt<HrOrganizationDao>();
  CustomerRecordDao get customerRecordDao => getIt<CustomerRecordDao>();
  CheckinPhotoDao get checkinPhotoDao => getIt<CheckinPhotoDao>();
  FeedbackDao get feedbackDao => getIt<FeedbackDao>();
  ChatListDao get chatListDao => getIt<ChatListDao>();
  ChatDao get chatDao => getIt<ChatDao>();
  CreateChatGroupDao get createChatGroupDao => getIt<CreateChatGroupDao>();
  GroupChatDetailDao get groupChatDetailDao => getIt<GroupChatDetailDao>();
  UserListDao get userListDao => getIt<UserListDao>();
  TagListDao get tagListDao => getIt<TagListDao>();
  StoryDetailDao get storyDetailDao => getIt<StoryDetailDao>();
  NotificationListDao get notificationListDao => getIt<NotificationListDao>();
  CreateChatFolderDao get createChatFolderDao => getIt<CreateChatFolderDao>();
  TicketDetailDao get ticketDetailDao => getIt<TicketDetailDao>();
  DevDao get devDao => getIt<DevDao>();
}
