// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_chat_detail_api_service.dart';

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers,unused_element

class _GroupChatDetailApiService implements GroupChatDetailApiService {
  _GroupChatDetailApiService(
    this._dio, {
    this.baseUrl,
    this.errorLogger,
  });

  final Dio _dio;

  String? baseUrl;

  final ParseErrorLogger? errorLogger;

  @override
  Future<HttpResponse<GroupChatDetailResponseModel?>> getGroupChatDetail(
    GroupChatDetailRequestParams groupChatDetailRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(groupChatDetailRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<HttpResponse<GroupChatDetailResponseModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/group-chat-detail',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GroupChatDetailResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : GroupChatDetailResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GroupChatDetailUpdateResponseModel?>>
      updateGroupChatDetail(
    GroupChatDetailUpdateRequestParams groupChatDetailUpdateRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = groupChatDetailUpdateRequestParams;
    final _options =
        _setStreamType<HttpResponse<GroupChatDetailUpdateResponseModel>>(
            Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
                .compose(
                  _dio.options,
                  '/update-group-chat-detail',
                  queryParameters: queryParameters,
                  data: _data,
                )
                .copyWith(
                    baseUrl: _combineBaseUrls(
                  _dio.options.baseUrl,
                  baseUrl,
                )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GroupChatDetailUpdateResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : GroupChatDetailUpdateResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GroupChatDetailMediaLoadResponseModel?>>
      mediaLoadGroupChatDetail(
    GroupChatDetailMediaLoadRequestParams
        groupChatDetailMediaLoadRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(groupChatDetailMediaLoadRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<HttpResponse<GroupChatDetailMediaLoadResponseModel>>(
            Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
                .compose(
                  _dio.options,
                  '/media-load-group-chat-detail',
                  queryParameters: queryParameters,
                  data: _data,
                )
                .copyWith(
                    baseUrl: _combineBaseUrls(
                  _dio.options.baseUrl,
                  baseUrl,
                )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GroupChatDetailMediaLoadResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : GroupChatDetailMediaLoadResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GroupChatDetailFileLoadResponseModel?>>
      fileLoadGroupChatDetail(
    GroupChatDetailFileLoadRequestParams groupChatDetailFileLoadRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(groupChatDetailFileLoadRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<HttpResponse<GroupChatDetailFileLoadResponseModel>>(
            Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
                .compose(
                  _dio.options,
                  '/file-load-group-chat-detail',
                  queryParameters: queryParameters,
                  data: _data,
                )
                .copyWith(
                    baseUrl: _combineBaseUrls(
                  _dio.options.baseUrl,
                  baseUrl,
                )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GroupChatDetailFileLoadResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : GroupChatDetailFileLoadResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GroupChatDetailLinkLoadResponseModel?>>
      linkLoadGroupChatDetail(
    GroupChatDetailLinkLoadRequestParams groupChatDetailLinkLoadRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(groupChatDetailLinkLoadRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<HttpResponse<GroupChatDetailLinkLoadResponseModel>>(
            Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
                .compose(
                  _dio.options,
                  '/link-load-group-chat-detail',
                  queryParameters: queryParameters,
                  data: _data,
                )
                .copyWith(
                    baseUrl: _combineBaseUrls(
                  _dio.options.baseUrl,
                  baseUrl,
                )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GroupChatDetailLinkLoadResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : GroupChatDetailLinkLoadResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GroupChatDetailAvatarUploadResponseModel?>>
      avatarUploadGroupChatDetail(
    dynamic groupChatDetailAvatarUploadRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = groupChatDetailAvatarUploadRequestParams;
    final _options =
        _setStreamType<HttpResponse<GroupChatDetailAvatarUploadResponseModel>>(
            Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
                .compose(
                  _dio.options,
                  '/theadvance/avatar/upload',
                  queryParameters: queryParameters,
                  data: _data,
                )
                .copyWith(
                    baseUrl: _combineBaseUrls(
                  _dio.options.baseUrl,
                  baseUrl,
                )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GroupChatDetailAvatarUploadResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : GroupChatDetailAvatarUploadResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GroupChatDetailMemberInfoLoadResponseModel?>>
      memberInfoLoadGroupChatDetail(
    GroupChatDetailMemberInfoLoadRequestParams
        groupChatDetailMemberInfoLoadRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(groupChatDetailMemberInfoLoadRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
        HttpResponse<GroupChatDetailMemberInfoLoadResponseModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/member-info-load-group-chat-detail',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GroupChatDetailMemberInfoLoadResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : GroupChatDetailMemberInfoLoadResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GroupChatDetailGetRuleByRoleResponseModel?>>
      getRuleByRoleGroupChatDetail(
    GroupChatDetailGetRuleByRoleRequestParams
        groupChatDetailGetRuleByRoleRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(groupChatDetailGetRuleByRoleRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<HttpResponse<GroupChatDetailGetRuleByRoleResponseModel>>(
            Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
                .compose(
                  _dio.options,
                  '/get-rule-by-role-group-chat-detail',
                  queryParameters: queryParameters,
                  data: _data,
                )
                .copyWith(
                    baseUrl: _combineBaseUrls(
                  _dio.options.baseUrl,
                  baseUrl,
                )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GroupChatDetailGetRuleByRoleResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : GroupChatDetailGetRuleByRoleResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GroupChatDetailGetUserRulesResponseModel?>>
      getUserRulesGroupChatDetail(
    GroupChatDetailGetUserRulesRequestParams
        groupChatDetailGetUserRulesRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.addAll(groupChatDetailGetUserRulesRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<HttpResponse<GroupChatDetailGetUserRulesResponseModel>>(
            Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
                .compose(
                  _dio.options,
                  '/get-user-rules-group-chat-detail',
                  queryParameters: queryParameters,
                  data: _data,
                )
                .copyWith(
                    baseUrl: _combineBaseUrls(
                  _dio.options.baseUrl,
                  baseUrl,
                )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GroupChatDetailGetUserRulesResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : GroupChatDetailGetUserRulesResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GroupChatDetailUpdateAdminRuleResponseModel?>>
      updateAdminRuleGroupChatDetail(
    GroupChatDetailUpdateAdminRuleRequestParams
        groupChatDetailUpdateAdminRuleRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = groupChatDetailUpdateAdminRuleRequestParams;
    final _options = _setStreamType<
        HttpResponse<GroupChatDetailUpdateAdminRuleResponseModel>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/update-admin-rule-group-chat-detail',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GroupChatDetailUpdateAdminRuleResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : GroupChatDetailUpdateAdminRuleResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GroupChatDetailUpdateMemberRuleResponseModel?>>
      updateMemberRuleGroupChatDetail(
    GroupChatDetailUpdateMemberRuleRequestParams
        groupChatDetailUpdateMemberRuleRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = groupChatDetailUpdateMemberRuleRequestParams;
    final _options = _setStreamType<
        HttpResponse<GroupChatDetailUpdateMemberRuleResponseModel>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/update-member-rule-group-chat-detail',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GroupChatDetailUpdateMemberRuleResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : GroupChatDetailUpdateMemberRuleResponseModel.fromJson(
              _result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GroupChatDetailChangeOwnerResponseModel?>>
      changeOwnerGroupChatDetail(
    GroupChatDetailChangeOwnerRequestParams
        groupChatDetailChangeOwnerRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    final _data = groupChatDetailChangeOwnerRequestParams;
    final _options =
        _setStreamType<HttpResponse<GroupChatDetailChangeOwnerResponseModel>>(
            Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
                .compose(
                  _dio.options,
                  '/change-owner-group-chat-detail',
                  queryParameters: queryParameters,
                  data: _data,
                )
                .copyWith(
                    baseUrl: _combineBaseUrls(
                  _dio.options.baseUrl,
                  baseUrl,
                )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GroupChatDetailChangeOwnerResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : GroupChatDetailChangeOwnerResponseModel.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  @override
  Future<HttpResponse<GroupChatDetailGetUserExceptionResponseModel?>>
      getUserExceptionGroupChatDetail(
    GroupChatDetailGetUserExceptionRequestParams
        groupChatDetailGetUserExceptionRequestParams, {
    bool? isMockUp,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters
        .addAll(groupChatDetailGetUserExceptionRequestParams.toJson());
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{r'isMockUp': isMockUp};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
        HttpResponse<GroupChatDetailGetUserExceptionResponseModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/get-user-exception-group-chat-detail',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late GroupChatDetailGetUserExceptionResponseModel? _value;
    try {
      _value = _result.data == null
          ? null
          : GroupChatDetailGetUserExceptionResponseModel.fromJson(
              _result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    final httpResponse = HttpResponse(_value, _result);
    return httpResponse;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }

  String _combineBaseUrls(
    String dioBaseUrl,
    String? baseUrl,
  ) {
    if (baseUrl == null || baseUrl.trim().isEmpty) {
      return dioBaseUrl;
    }

    final url = Uri.parse(baseUrl);

    if (url.isAbsolute) {
      return url.toString();
    }

    return Uri.parse(dioBaseUrl).resolveUri(url).toString();
  }
}
