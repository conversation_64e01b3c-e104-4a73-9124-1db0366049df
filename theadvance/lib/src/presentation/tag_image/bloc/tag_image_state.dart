part of 'tag_image_bloc.dart';

enum TagImageStatus {
  init,
  loading,
  success,
  loadingGetMore,
  getMoreSuccess,
  comboTagSuccess,
  imageByComboTagSuccess,
  uploadSuccess,
  updateSuccess,
  onlySetSuccess,
  removeSetSuccess,
  updateSetSuccess,
  failure,
}

@immutable
class TagImageState extends Equatable {
  const TagImageState(this.status, {this.data,this.roomCode,});
  final dynamic data;
  final TagImageStatus status;
  final String? roomCode;

  TagImageState copyWith<T>(final TagImageStatus status, 
  {final dynamic data, final String? roomCode,
  }) {
    return TagImageState(status, data: data, roomCode: roomCode);
  }

  @override
  List<Object?> get props => [status, data, roomCode];
}
