// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';
import 'package:collection/collection.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';

// Project imports:
import '../../../../core/utils/nd_utils.dart';
import '../../../../data/datasources/ez_datasources.dart';
import '../../../../data/models/nd_models.dart';
import '../../../../injector/injector.dart';
import '../../../product_confirm/widgets/base_layout.dart';
import '../../../widgets/chat_search_bar.dart';
import '../../../widgets/widgets.dart';
import '../../home/<USER>';
import '../../home/<USER>/home_menu_item_favorite.dart';
import '../bloc/edit_home_menu_bloc.dart';
import '../widgets/edit_menu_item.dart';
import 'edit_home_menu_item.dart';
import 'edit_home_menu_target_item.dart';

@RoutePage()
class EditHomeMenuScreen extends StatelessWidget {
  const EditHomeMenuScreen({final Key? key}) : super(key: key);

  @override
  Widget build(final BuildContext context) {
    return BlocProvider(
      create: (final context) =>
          getIt<EditHomeMenuBloc>()..add(EditHomeMenuFetched()),
      child: const EditHomeMenuView(),
    );
  }
}

class EditHomeMenuView extends StatefulWidget {
  const EditHomeMenuView({final Key? key}) : super(key: key);

  @override
  EditHomeMenuViewState createState() => EditHomeMenuViewState();
}

class EditHomeMenuViewState extends State<EditHomeMenuView> {
  List<ServiceItemsModel?>? data;
  late Map<String, bool> checkListHomeMenu;
  final searchController = TextEditingController();
  List<ServiceItemsModel?> searchData = [];
  List<String?> favoriteCodeList = [];
  final GlobalKey _draggableKey = GlobalKey();
  final ValueNotifierList<ServiceItemsModel> dragMain = ValueNotifierList([
    ServiceItemsModel(),
    ServiceItemsModel(),
  ]);
  final ValueNotifierList<ServiceItemsModel> dragPin = ValueNotifierList([
    ServiceItemsModel(),
    ServiceItemsModel(),
    ServiceItemsModel(),
    ServiceItemsModel(),
  ]);
    final ValueNotifierList<ServiceItemsModel> dragMainInit = ValueNotifierList([
    ServiceItemsModel(),
    ServiceItemsModel(),
  ]);
  final ValueNotifierList<ServiceItemsModel> dragPinInit = ValueNotifierList([
    ServiceItemsModel(),
    ServiceItemsModel(),
    ServiceItemsModel(),
    ServiceItemsModel(),
  ]);
  @override
  void initState() {
    super.initState();
    checkListHomeMenu = {};
    WidgetsBinding.instance.addPostFrameCallback((final _) async {
     await _loadFavoriteServices();
    });
  }
  Future<void> _loadFavoriteServices() async {
    try {

      final cachePin = await EZCache.shared.getHomeItem(
        CollaboratorKeys.pinSettingServices,
      );
      final cacheMain = await EZCache.shared.getHomeItem(
       CollaboratorKeys.mainSettingServices,
      );

      if (cachePin.isNotEmpty) {
        dragPinInit.setValue(cachePin.map((final e)
       => e??ServiceItemsModel()).toList());
      }
      if (cacheMain.isNotEmpty) {
        dragMainInit.setValue(cacheMain.map((final e)
       => e??ServiceItemsModel()).toList());
      }

    } catch (e) {

    }
  }
  @override
  Widget build(final BuildContext context) {
   return BaseLayout(
          title: Text(
            context.l10n.custom,
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontWeight: FontWeight.w500,
              fontSize: 18,
            ),
          ),
          actions: [
            IconButton(
              onPressed: () {
            EZCache.shared.saveHomeItem( CollaboratorKeys.mainSettingServices,
            dragMainInit.value);
            EZCache.shared.saveHomeItem( CollaboratorKeys.pinSettingServices,
            dragPinInit.value);
                final mains = dragMain.value.map((final e) => e.code??'')
                .toList();
                final pins = dragPin.value.map((final e) => e.code??'')
                .toList();
               final services = HomeBody.services.value;
               final items = services.where((final e)
               => !mains.contains(e?.code) && !pins.contains(e?.code)).toList();
                 for (var i = 0; i < dragPin.value.length; i++) {
                    if(dragPin.value[i].code == null){
                      dragPin.updateValuebyIndex(i,
                       items.first?? ServiceItemsModel());
                      items.removeAt(0);
                    }
                }
                  for (var i = 0; i < dragMain.value.length; i++) {
                    if(dragMain.value[i].code == null){
                      dragMain.updateValuebyIndex(i,
                       items.first?? ServiceItemsModel());
                      items.removeAt(0);
                    }
                }

     
           EZCache.shared.saveHomeItem( CollaboratorKeys.mainServices,
            dragMain.value);
            EZCache.shared.saveHomeItem( CollaboratorKeys.pinServices,
            dragPin.value);
           EzToast.showShortToast(message: context.l10n.updateSuccess);
              },
              icon: Text(
                context.l10n.save,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontSize: 15,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
          ],
          body: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                  Text(
                  context.l10n.featureList,
                  style: Theme.of(context).textTheme.titleSmall,
                ),
                const SizedBox(height: 12),
                   Container(
                  padding: const EdgeInsets.all(16),
                  decoration: ShapeDecoration(
                    color: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: ValueListenableBuilder(
                    valueListenable: dragPinInit,
                    builder: (final context, final vSelected, final child) {
                      return Row(
                        children: [
                          ...List.generate(vSelected.length, (final iDrag){
                            final item = vSelected[iDrag];
                            return   Expanded(
                              child: item.url == null
                                  ? EditHomeMenuTargetItem(
                                    dragInitTarget: dragPinInit,
                                    dragTarget: dragPin,
                                   index: iDrag)
                                  : EditHomeMenuItem(service: item),
                            );
                          })
                        
                        ],
                      );
                    },
                  ),
                  // buildFavoriteMenu(
                  //   context,
                  //   data: favoriteCodeList
                  //       .map(
                  //         (final e) => data?.firstWhereOrNull(
                  //           (final element) => element?.code == e,
                  //         ),
                  //       )
                  //       .toList(),
                  // ),
                ),
                  const SizedBox(height: 12),
                  Text(
                  context.l10n.featureList,
                  style: Theme.of(context).textTheme.titleSmall,
                ),
               const SizedBox(height: 12,),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: ShapeDecoration(
                    color: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: ValueListenableBuilder(
                    valueListenable: dragMainInit,
                    builder: (final context,
                     final vSelected,
                     final child) {
                      return Row(
                        children: [
                          ...List.generate(vSelected.length, (final iDrag){
                            final item = vSelected[iDrag];
                            return   Expanded(
                              child: item.url == null
                                  ? EditHomeMenuTargetItem(
                                    dragInitTarget: dragMainInit,
                                    dragTarget: dragMain, index: iDrag)
                                  : EditHomeMenuItem(service: item),
                            );
                          })
                        
                        ],
                      );
                    },
                  ),
                ),
                  const SizedBox(height: 12),
                Text(
                  context.l10n.featureList,
                  style: Theme.of(context).textTheme.titleSmall,
                ),
                const SizedBox(height: 12),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: ShapeDecoration(
                      color: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Column(
                      children: [
                        // ChatSearchBar(
                        //   controller: searchController,
                        //   hintText: context.l10n.searchByFeature,
                        //   onChanged: (final key) {
                        //     searchData =
                        //         data
                        //             ?.where(
                        //               (final element) =>
                        //                   element?.name
                        //                       ?.toLowerCase()
                        //                       .contains(key.toLowerCase()) ??
                        //                   false,
                        //             )
                        //             .toList() ??
                        //         [];
                        //     setState(() {});
                        //   },
                        // ),
                        Expanded(
                          child: ValueListenableBuilder(
                            valueListenable: dragMainInit,
                            builder: (final context, final vMain,final chilf) {
                              return ValueListenableBuilder(
                                valueListenable: dragPinInit,
                                builder: (final context,
                                 final vPin,
                                final child) {
                                  final List<ServiceItemsModel> allServices = [
                                    ...vMain,
                                    ...vPin,
                                  ];
                                  return ValueListenableBuilder(
                                    valueListenable: HomeBody.services,
                                    builder:
                                        (final context, final vServices, final child) {
                                          final buildServiceWidgets = vServices
                                              .map(
                                                (final e) =>
                                                    Stack(
                                                      children: [
                                                        Positioned.fill(
                                                          child: LongPressDraggable<
                                                            ServiceItemsModel
                                                          >(
                                                            data: e,
                                                            dragAnchorStrategy:
                                                                pointerDragAnchorStrategy,
                                                            feedback: _DraggingListItem(
                                                              dragKey: _draggableKey,
                                                              child: EditHomeMenuItem(
                                                                service: e,
                                                              ),
                                                            ),
                                                            child: Center(
                                                              child: EditHomeMenuItem(
                                                                service: e,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                    if(allServices.indexWhere((final item)
                                                    => e?.code == item.code) != -1)
                                                    Positioned.fill(child: 
                                                    ColoredBox(color:
                                                     Colors.white.withValues(alpha: .65)))
                                                      ],
                                                    ),
                                              )
                                              .toList();
                                          return GridView.count(
                                            crossAxisCount: 4,
                                            shrinkWrap: true,
                                            
                                            mainAxisSpacing: 16,
                                            crossAxisSpacing: 16,
                                            childAspectRatio: .82,
                                            children: buildServiceWidgets,
                                          );
                                        },
                                  );
                                },
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
  }

  Widget buildFavoriteMenu(
    final BuildContext context, {
    required final List<ServiceItemsModel?> data,
  }) {
    return ValueListenableBuilder(
      valueListenable: dragPin,
      builder: (final context, final vSelected, final child) {
        return Row(
          children: [
            for (final ServiceItemsModel item in vSelected)
              Expanded(
                child: item.url == null
                    ? const SizedBox.square(
                        dimension: 45,
                        child: ColoredBox(color: Colors.white),
                      )
                    : EditHomeMenuItem(service: item),
              ),
          ],
        );
      },
    );
  }

  ListView buildMenu(
    final BuildContext context, {
    required final List<ServiceItemsModel?> data,
  }) {
    return ListView.separated(
      physics: const BouncingScrollPhysics(),
      shrinkWrap: true,
      itemBuilder: (final _, final index) => EditMenuItem(
        image: ClipRRect(
          borderRadius: BorderRadius.circular(24),
          child: EzCachedNetworkImage(
            imageUrl: data[index]?.url ?? '',
            width: 40,
            height: 40,
          ),
        ),
        title: data[index]?.name ?? '',
        button: IconButton(
          onPressed: () async {
            if (favoriteCodeList.contains(data[index]?.code)) {
              return;
            }
            favoriteCodeList.add(data[index]?.code);
            setState(() {});
          },
          icon: EZResources.image(
            ImageParams(
              name: AppIcons.icAddCircle,
              size: const ImageSize(24, 24),
            ),
          ),
        ),
      ),
      separatorBuilder: (final context, final index) => Container(
        height: 0.35,
        margin: const EdgeInsets.symmetric(vertical: 4),
        color: const Color(0xFFC7C7C7),
      ),
      itemCount: data.length,
    );
  }
}

class _DraggingListItem extends StatelessWidget {
  const _DraggingListItem({
    required this.dragKey,
    required this.child,
  });

  final GlobalKey dragKey;
  final Widget child;

  @override
  Widget build(final BuildContext context) {
    return FractionalTranslation(
      translation: const Offset(-0.5, -0.2),
      child: ClipRRect(
        key: dragKey,
        borderRadius: BorderRadius.circular(12),
        child: SizedBox(
          height: 300,
          width: 300,
          child: Opacity(opacity: 0.86, child: child),
        ),
      ),
    );
  }
}
