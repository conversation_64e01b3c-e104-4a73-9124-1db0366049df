// Flutter imports:
import 'package:ez_resources/ez_resources.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';
import 'package:ez_core/ez_core.dart';

// Project imports:
import '../../../../core/routes/routes.dart';
import '../../../../data/datasources/local/cache/hive/ez_cache.dart';
import '../../../../data/datasources/local/key/keys.dart';
import '../../../../data/models/api_models.dart';
import '../../../../injector/injector.dart';
import '../../../_blocs/general_bloc/general_bloc.dart';
import '../../../product_confirm/widgets/base_layout.dart';
import '../../../widgets/value_notifier_list.dart';
import '../../../widgets/widgets.dart';
import '../bloc/home_bloc.dart';
import '../widgets/widgets.dart';

@RoutePage()
class HomePage extends StatefulWidget {
  const HomePage({final Key? key}) : super(key: key);
  @override
  HomePageState createState() => HomePageState();
}

class HomePageState extends State<HomePage> {
  final ValueNotifierList<ServiceItemsModel?> favoriteCodeList = ValueNotifierList([]);
  final ValueNotifierList<ServiceItemsModel?> mainCodeList = ValueNotifierList([]);
  UserModel? user;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((final t) async {
      await _loadFavoriteServices();
      await _loadUserProfile();
    });
  }

  @override
  Widget build(final BuildContext context) {
    final isRecording =
        context.watch<GeneralBloc>().recorderData.controller?.isRecording ??
        false;
    return BlocProvider(
      create: (final context) => getIt<HomeBloc>()..add(HomeFetched()),
      child: BaseLayout(
        leading: _buildUserProfile(user),
         actions: [
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: IconButton(
              onPressed: () async {
                context.router.pushNamed(Routes.notifications);
              },
              icon: EZResources.image(
                ImageParams(
                  name: AppIcons.icNotiOutline,
                  size: const ImageSize(22, 22),
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
          ),
          IconButton(
            onPressed: () async {
              context.router.pushNamed(Routes.editHomeMenu).then((
                final _,
              ) async {
                await _loadFavoriteServices();
              });
            },
            icon: EZResources.image(ImageParams(name: AppIcons.icCustom)),
          ),
         ],
        title: const SizedBox.shrink(),
        body: HomeBody(
         favoriteCodeList: favoriteCodeList,
         mainCodeList: mainCodeList),
      ),
    );
  }

  Widget _buildUserProfile(final UserModel? user) {
    return ClipOval(
      child: EzCachedNetworkImage(
        imageUrl: user?.avatar,
        fit: BoxFit.contain,
        width: _itemWidth(context),
        height: _itemWidth(context),
      ),
    );
  }

  double _itemWidth(final BuildContext context) {
    return MediaQuery.sizeOf(context).width / 8.5;
  }

  Future<void> _loadFavoriteServices() async {
    try {

      final cachePin = await EZCache.shared.getHomeItem(
        CollaboratorKeys.pinServices,
      );
      final cacheMain = await EZCache.shared.getHomeItem(
        CollaboratorKeys.mainServices,
      );

      if (cachePin.isNotEmpty) {
        favoriteCodeList.setValue(cachePin.map((final e) => e).toList());
      }
      if (cacheMain.isNotEmpty) {
         mainCodeList.setValue(cacheMain.map((final e) => e).toList());
      }
 
    } catch (e) {
        debugPrint(e.toString());
    }
  }

  Future<void> _loadUserProfile() async {
    try {
      user = EZCache.shared.getUserProfile();
    } catch (_) {}
  }

  // Method to force reload favorite services (useful for debugging)
  void reloadFavoriteServices() {
    print('🔄 Force reloading favorite services...');
    _loadFavoriteServices();
  }
}
