import 'dart:ui';

import 'package:ez_resources/ez_resources.dart';
import 'package:flutter/material.dart';

import '../../../widgets/widgets.dart';

class SnakeBorderAnimation extends StatefulWidget {
  const SnakeBorderAnimation({
    super.key,
    this.width,
    this.height,
    this.borderRadius,
    required this.background,
    this.title,
    required this.color,
    required this.icon,
  });
  final String? title;
  final double? width;
  final double? height;
  final double? borderRadius;
  final ImageProvider background;
  final Widget icon;
  final Color color;

  @override
  State<SnakeBorderAnimation> createState() => _SnakeBorderAnimationState();
}

class _SnakeBorderAnimationState extends State<SnakeBorderAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  bool vIsComplete = false;
  @override
  void initState() {
    super.initState();
    _controller =
        AnimationController(
            vsync: this,
            duration: const Duration(milliseconds: 1500),
          )
          ..repeat();
          // .whenComplete(() {
          //   setState(() {
          //     vIsComplete = true;
          //   });
          // });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (final BuildContext context, final Widget? child) {
        return Padding(
          padding: const EdgeInsets.all(2.0),
          child: CustomPaint(
            painter: SnakePainter(
              animation: _controller,
              color: vIsComplete
                  ? Colors.white.withValues(alpha: 0)
                  : widget.color,
            ),
            child: child,
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          image: DecorationImage(image: widget.background, fit: BoxFit.cover),
          color: const Color(0xFF1F2937),
          borderRadius: BorderRadius.circular(widget.borderRadius ?? 16),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          child: Row(
            spacing: 8,
            children: [
              widget.icon,
              Expanded(
                child: Text(
                  widget.title ?? '',
                 textScaler: TextScaler.noScaling,
                      maxLines: 2,
                      textAlign: TextAlign.start,
                      style: Theme.of(context).textTheme.labelLarge?.copyWith(
                        
                        color: Colors.white),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// A custom painter that draws a "snake" border animation.
class SnakePainter extends CustomPainter {
  SnakePainter({required this.animation, required this.color})
    : super(repaint: animation);
  final Animation<double> animation;
  final Color color;
  @override
  void paint(final Canvas canvas, final Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4.0;

    final path = Path()
      ..addRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(0, 0, size.width, size.height),
          const Radius.circular(16),
        ),
      );

    final PathMetric pathMetric = path.computeMetrics().first;
    final double totalLength = pathMetric.length;

    const double snakeLength = 100.0;

    final double animatedDistance = totalLength * animation.value;

    final double startSegment = animatedDistance - snakeLength;
    final double endSegment = animatedDistance;

    Path currentSnakePath = Path();

    Offset? startPoint;
    Offset? endPoint;

    if (startSegment < 0) {
      currentSnakePath.addPath(
        pathMetric.extractPath(totalLength + startSegment, totalLength),
        Offset.zero,
      );
      currentSnakePath.addPath(
        pathMetric.extractPath(0, endSegment),
        Offset.zero,
      );

      startPoint = pathMetric
          .getTangentForOffset(totalLength + startSegment)
          ?.position;
      endPoint = pathMetric.getTangentForOffset(endSegment)?.position;
    } else {
      currentSnakePath = pathMetric.extractPath(startSegment, endSegment);

      startPoint = pathMetric.getTangentForOffset(startSegment)?.position;
      endPoint = pathMetric.getTangentForOffset(endSegment)?.position;
    }

    if (startPoint != null && endPoint != null) {
      paint.shader = LinearGradient(
        colors: <Color>[color.withValues(alpha: .1), color],

        begin: Alignment(
          (startPoint.dx / size.width) * 2 - 1,
          (startPoint.dy / size.height) * 2 - 1,
        ),
        end: Alignment(
          (endPoint.dx / size.width) * 2 - 1,
          (endPoint.dy / size.height) * 2 - 1,
        ),
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));
    }

    canvas.drawPath(currentSnakePath, paint);
  }

  @override
  bool shouldRepaint(covariant final SnakePainter oldDelegate) {
    return animation != oldDelegate.animation;
  }
}
