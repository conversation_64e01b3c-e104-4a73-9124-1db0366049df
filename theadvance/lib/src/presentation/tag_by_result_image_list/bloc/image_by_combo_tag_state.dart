part of 'image_by_combo_tag_bloc.dart';


enum ImageByComboTagStatus {
  init,
  loading,
  success,
  removeImageSuccess,
  removeTagSuccess,
  failure,
}

@immutable
class ImageByComboTagState extends Equatable {
  const ImageByComboTagState(this.status, {this.data});
  final dynamic data;
  final ImageByComboTagStatus status;

  ImageByComboTagState copyWith<T>(
    final ImageByComboTagStatus status, {final dynamic data}) {
    return ImageByComboTagState(status, data: data);
  }

  @override
  List<Object?> get props => [status, data];
}
