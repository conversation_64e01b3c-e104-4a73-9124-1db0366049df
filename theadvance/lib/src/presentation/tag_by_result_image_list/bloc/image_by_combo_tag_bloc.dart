import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:ez_core/ez_core.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';

import '../../../core/params/image_by_combo_tag_params.dart';
import '../../../domain/usecases/tag_image/get_image_by_combo_tag_usecase.dart';

part 'image_by_combo_tag_event.dart';
part 'image_by_combo_tag_state.dart';

@injectable
class ImageByComboTagBloc 
extends Bloc<ImageByComboTagEvent, ImageByComboTagState> {
  ImageByComboTagBloc(this._getImageByComboTagUseCase) : super(
    const ImageByComboTagState(ImageByComboTagStatus.init)) {
      on<ImageByComboTagGetMore>(_onImageByComboTagGetPage);
      // on<ImageByComboTagRemoveImage>(_onImageByComboTagRemoveImage);
      // on<ImageByComboTagRemoveTag>(_onImageByComboTagRemoveTag);
  }
    final GetImageByComboTagUsecae _getImageByComboTagUseCase;

    FutureOr<void> _onImageByComboTagGetPage(
    final ImageByComboTagGetMore event,
    final Emitter<ImageByComboTagState> emit,
  ) async {
    emit(state.copyWith(ImageByComboTagStatus.loading));
    final dataState = await _getImageByComboTagUseCase(params: event.params);
    if (dataState is DataSuccess) {
      emit(
        state.copyWith(
          ImageByComboTagStatus.success,
          data: dataState.data,
        ),
      );
    }
    if (dataState is DataFailure) {
      emit(
        state.copyWith(ImageByComboTagStatus.failure, data: dataState.error),
      );
    }
  }

}
