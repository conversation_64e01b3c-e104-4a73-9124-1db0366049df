part of 'image_by_combo_tag_bloc.dart';

@immutable
abstract class ImageByComboTagEvent {}

class ImageByComboTagGetMore extends ImageByComboTagEvent {
  ImageByComboTagGetMore(this.params);

  final ImageByComboTagQueryParams params;
}

class ImageByComboTagRemoveImage extends ImageByComboTagEvent {
  ImageByComboTagRemoveImage(this.params);

  final ImageByComboTagQueryParams params;
}

class ImageByComboTagRemoveTag extends ImageByComboTagEvent {
  ImageByComboTagRemoveTag(this.params);

  final ImageByComboTagQueryParams params;
}
