import 'package:flutter/material.dart';

import '../../../domain/entities/combo_tag.dart';
import '../../../domain/entities/customer_get_room_list.dart';
import '../../../domain/entities/image_by_combo_tag.dart';
import '../../tag_image/bloc/tag_image_bloc.dart';
import '../../widgets/widgets.dart';
import 'custom_indicator_tabbar.dart';
import 'tag_by_room_tab.dart';

class TagByResultImageListBody extends StatefulWidget {
  const TagByResultImageListBody({
    super.key,
    required this.tags,
    required this.rooms,
    required this.tabController,
  });
  final ValueNotifierList<ComboTag> tags;
  final List<CustomerGetRoomListItem> rooms;
  final TabController tabController;
  @override
  State<TagByResultImageListBody> createState() =>
      _TagByResultImageListBodyState();
}

class _TagByResultImageListBodyState extends State<TagByResultImageListBody>
    with TickerProviderStateMixin {
  final ValueNotifierList<ImageByComboTag?> images = ValueNotifierList([]);
  final ValueNotifierList<ComboTag> comboTagSelected = ValueNotifierList([]);

  @override
  void initState() {
    super.initState();
    // Khởi tạo TabController với số lượng tabs bằng số lượng rooms
    // Đảm bảo có ít nhất 1 tab
    if (widget.tags.value.isNotEmpty) {
      final index = widget.rooms.indexWhere(
        (final room) => room.roomCode == widget.tags.value.first.itemGroupId,
      );
      if (index != -1) {
        widget.tabController.animateTo(index);
      }
    }
  }

  @override
  Widget build(final BuildContext context) {
    return BlocConsumer<TagImageBloc, TagImageState>(
      listener: (final context, final state) {
        if (state.status == TagImageStatus.imageByComboTagSuccess) {
          final List<ImageByComboTag> data = state.data;
          images.setValue(data);
        }
      },
      builder: (final context, final state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: TabBar(
                tabAlignment: TabAlignment.start,
                controller: widget.tabController,
                isScrollable: true,
                indicatorColor: Theme.of(context).primaryColor,
                indicatorWeight: 0,
                indicator: CustomTabIndicator(
                  indicatorHeight: 2,
                  color: Theme.of(context).primaryColor,
                ),
                indicatorSize: TabBarIndicatorSize.tab,
                labelColor: Theme.of(context).primaryColor,
                unselectedLabelColor: Theme.of(
                  context,
                ).hintColor.withValues(alpha: .8),
                labelStyle: Theme.of(context).textTheme.labelLarge?.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                unselectedLabelStyle: Theme.of(context).textTheme.labelLarge
                    ?.copyWith(fontSize: 16, fontWeight: FontWeight.w500),
                dividerColor: Colors.transparent,
                tabs: widget.rooms.isNotEmpty
                    ? widget.rooms
                          .map(
                            (final room) =>
                                Tab(height: 32, text: room.roomName ?? 'Room'),
                          )
                          .toList()
                    : [const Tab(text: 'No Rooms')],
              ),
            ),
            const Divider(thickness: .9),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: _buildTabView(),
              ),
            ),
          ],
        );
      },
    );
  }

  TabBarView _buildTabView() {
    return TabBarView(
      controller: widget.tabController,
      children: widget.rooms.isNotEmpty
          ? widget.rooms
                .map(
                  (final room) => ValueListenableBuilder(
                    valueListenable: images,
                    builder: (final context, final vImage, final child) {
                      final imageByRoom = vImage
                          .map((final e) {
                            if (e?.images == null) {
                              return null;
                            }

                            final filteredImages = e!.images.where((
                              final image,
                            ) {
                              return image?.tags?.any(
                                    (final tag) =>
                                        tag.itemGroupId == room.roomCode,
                                  ) ??
                                  false;
                            }).toList();

                            if (filteredImages.isNotEmpty) {
                              return ImageByComboTag(
                                createdDate: e.createdDate,
                                images: filteredImages,
                              );
                            }
                            return null;
                          })
                          .where((final e) => e != null)
                          .toList();

                      return ValueListenableBuilder(
                        valueListenable: widget.tags,
                        builder: (final context, final vTags, final child) {
                          final tagByRoom = vTags
                              .where(
                                (final tagRoom) =>
                                    tagRoom.itemGroupId == room.roomCode,
                              )
                              .toList();
                          return TagByRoomTab(
                            imageByRoom: imageByRoom,
                            tagByRoom: tagByRoom,
                            tags: widget.tags,
                            roomCode: room.roomCode ?? '',
                          );
                        },
                      );
                    },
                  ),
                )
                .toList()
          : [const Center(child: Text('No rooms available'))],
    );
  }
}
