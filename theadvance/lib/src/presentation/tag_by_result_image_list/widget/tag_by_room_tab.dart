import 'package:app_ui/app_ui.dart';
import 'package:auto_route/auto_route.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';
import 'package:flutter/material.dart';

import '../../../core/params/image_by_combo_tag_params.dart';
import '../../../core/params/story_write_info_request_params.dart';
import '../../../core/routes/app_router.dart';
import '../../../domain/entities/combo_tag.dart';
import '../../../domain/entities/image_by_combo_tag.dart';
import '../../../injector/injector.dart';
import '../../story_list/widgets/story_image.dart';
import '../../tag_image/bloc/tag_image_bloc.dart';
import '../../widgets/value_notifier_list.dart';
import '../bloc/image_by_combo_tag_bloc.dart';
import '../view/preview_image_page.dart';

class TagByRoomTab extends StatefulWidget {
  const TagByRoomTab({
    super.key,
    required this.imageByRoom,
    required this.tagByRoom,
    required this.tags,
    required this.roomCode,
  });
  final String roomCode;
  final List<ImageByComboTag?> imageByRoom;
  final List<ComboTag> tagByRoom;
  final ValueNotifierList<ComboTag> tags;

  @override
  State<TagByRoomTab> createState() => _TagByRoomTabState();
}

class _TagByRoomTabState extends State<TagByRoomTab> {
  late ScrollController _scrollController;
  final ValueNotifierList<ImageByComboTag?> imageByRoom = ValueNotifierList([]);
  int page = 1;
  @override
  void initState() {
    imageByRoom.setValue(widget.imageByRoom);
    _scrollController = ScrollController()
      ..addListener(() {
        final maxScroll = _scrollController.position.maxScrollExtent;
        final currentScroll = _scrollController.offset;
        if (maxScroll > 300) {
          _lazyStoryAll(maxScroll, currentScroll);
        }
      });
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  final ValueNotifier<bool> isLoadingMore = ValueNotifier(false);
  final ValueNotifier<bool> isEndList = ValueNotifier(false);
  void _lazyStoryAll(final double maxScroll, final double currentScroll) {
    if (!isEndList.value) {
      if (!isLoadingMore.value) {
        if (currentScroll >= (maxScroll * 0.9)) {
          page++;
          context.read<ImageByComboTagBloc>().add(
            ImageByComboTagGetMore(
              ImageByComboTagQueryParams(
                itemGroupIds: widget.roomCode,
                tagIds: widget.tagByRoom
                    .map((final tag) => tag.tagId)
                    .join(','),
                page: page,
              ),
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(final BuildContext context) {
    return BlocProvider(
      create: (final context) => getIt<ImageByComboTagBloc>(),
      child: BlocConsumer<ImagBloc, ImagState>(
        listener: (context, state) {
          // TODO: implement listener
        },
        builder: (context, state) {
          return ValueListenableBuilder(
            valueListenable: imageByRoom,
            builder: (context, vImageByRoom, child) {
              return Column(
                spacing: 4,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (widget.tagByRoom.isNotEmpty &&
                      widget.tagByRoom.first.tagId != null)
                    _buildTagChip(widget.tagByRoom, context),
                  if ((widget.tagByRoom.isNotEmpty &&
                          widget.tagByRoom.first.tagId == null) ||
                      widget.tagByRoom.isEmpty)
                    const SizedBox(height: 24),
                  for (final image in vImageByRoom)
                    MediaQuery.removePadding(
                      context: context,
                      removeTop: true,
                      child: GridView.count(
                        controller: _scrollController,
                        crossAxisCount: 3,
                        shrinkWrap: true,
                        physics: const BouncingScrollPhysics(),
                        mainAxisSpacing: 4,
                        crossAxisSpacing: 4,
                        children: [
                          ...List.generate(image?.images.length ?? 0, (
                            final i,
                          ) {
                            final imageItem = image?.images[i];
                            return Stack(
                              fit: StackFit.passthrough,
                              children: [
                                StoryImage(
                                  originalWidth: 1,
                                  originalHeight: 1,
                                  tags: imageItem?.imageUrl ?? '',
                                  imageUrl: '${imageItem?.imageUrl}',
                                ),
                                GestureDetector(
                                  onTap: () {
                                    handleMedia(
                                      image?.images,
                                      imageItem?.imageUrl ?? '',
                                    );
                                    pushStoryImageDetail(context, imageItem, i);
                                  },
                                  child: _buildOverlay(Attachment()),
                                ),
                              ],
                            );
                          }),
                        ],
                      ),
                    ),
                ],
              );
            },
          );
        },
      ),
    );
  }

  Padding _buildTagChip(
    final List<ComboTag> tagByRoom,
    final BuildContext context,
  ) {
    return Padding(
      padding: const EdgeInsets.only(left: 12, right: 12, top: 16, bottom: 24),
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: [
          ...List.generate(
            tagByRoom.where((final e) => e.tagId != null).length,
            (final i) {
              final tag = tagByRoom
                  .where((final e) => e.tagId != null)
                  .toList()[i];
              return GestureDetector(
                onTap: () {
                  widget.tags.remove(tag);
                  context.read<TagImageBloc>().add(
                    ImageByComboTagGet(
                      ImageByComboTagQueryParams(
                        itemGroupIds: tag.itemGroupId,
                        tagIds: tagByRoom
                            .where((final e) => e.tagId != null)
                            .map((final tag) => tag.tagId)
                            .join(','),
                      ),
                    ),
                  );
                },
                child: DecoratedBox(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      width: .35,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: 4,
                      horizontal: 8,
                    ),
                    child: Row(
                      spacing: 4,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          tag.tagName ?? '',
                          style: TextStyle(
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                        Icon(
                          Icons.cancel,
                          color: Theme.of(context).primaryColor,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
          if (tagByRoom.length > 1) _buildDeleteAll(tagByRoom, context),
        ],
      ),
    );
  }

  GestureDetector _buildDeleteAll(
    final List<ComboTag> tagByRoom,
    final BuildContext context,
  ) {
    return GestureDetector(
      onTap: () {
        // ignore: prefer_foreach
        for (final ComboTag element in tagByRoom) {
          widget.tags.remove(element);
        }
      },
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.black45, width: .35),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 12),
          child: Row(
            spacing: 4,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                context.l10n.deleteAll,
                style: const TextStyle(
                  color: Colors.black45,
                  fontWeight: FontWeight.w400,
                ),
              ),
              EZResources.image(
                ImageParams(
                  name: AppIcons.icTrash,
                  color: Colors.black87,
                  size: const ImageSize.square(24),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  AspectRatio _buildOverlay(final Attachment? item) {
    return AspectRatio(
      aspectRatio:
          ((item?.width ?? 1) == 0 ? 1 : (item?.width ?? 1)) /
          ((item?.height ?? 1) == 0 ? 1 : (item?.height ?? 1)),
      child: Container(color: Colors.transparent),
    );
  }

  void handleMedia(
    final List<ImageByComboTagImages?>? items,
    final String tag,
  ) {
    final safeImageList = items
        ?.map((final e) => e ?? ImageByComboTagImages(tags: []))
        .toList();
    final listWidgetAttachment = safeImageList?.map((final eWidget) {
      return StoryImage(
        originalWidth: 1,
        originalHeight: 1,
        tags: tag,
        imageUrl: '${eWidget.imageUrl}',
        fit: BoxFit.contain,
        disableGestures: null,
        initialScale: PhotoViewComputedScale.contained,
        minScale: PhotoViewComputedScale.contained,
        maxScale: PhotoViewComputedScale.contained * 1.1,
      );
    }).toList();
    PreviewImagePage.listMedia.setValue(listWidgetAttachment);
  }

  Future<void> pushStoryImageDetail(
    final BuildContext context,
    final ImageByComboTagImages? item,
    final int i,
  ) async {
    context.router.push(PreviewImageRoute(initIndex: i));
  }
}
