// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:auto_route/auto_route.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';

import '../../story_list/widgets/story_image.dart';
import '../../widgets/value_notifier_list.dart';

@RoutePage()
class PreviewImagePage extends StatefulWidget {
  const PreviewImagePage({final Key? key, required this.initIndex})
    : super(key: key);
  final int initIndex;
  static ValueNotifierList<Widget> listMedia = ValueNotifierList([]);
  static late PageController pageController;
  @override
  State<PreviewImagePage> createState() => _PreviewImagePageState();
}

class _PreviewImagePageState extends State<PreviewImagePage> {
  @override
  void initState() {
    StoryImage.currentIdx = widget.initIndex;
    PreviewImagePage.pageController = PageController(
      initialPage: widget.initIndex,
    );
    super.initState();
  }

  @override
  Widget build(final BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.black,
        leading: IconButton(
          onPressed: () async {
            context.router.popForced();
          },
          icon: const Icon(Icons.arrow_back),
        ),
        actions: [
          GestureDetector(
            onTapDown: (details) => showMenu(
              context: context,
              position: RelativeRect.fromLTRB(
                MediaQuery.sizeOf(context).width / 2 - 50,
                details.globalPosition.dy + 10,
                 MediaQuery.sizeOf(context).width - details.globalPosition.dx,
             MediaQuery.sizeOf(context).height - details.globalPosition.dy,
              ),
              items: [
                 PopupMenuItem(
                  child: Text('Xoa anh'),
                  onTap: () {
                    context.router.popForced();
                  },
                ),
                PopupMenuItem(
                  child: Text('Xoa tag'),
                  onTap: () {
                    context.router.popForced();
                  },
                ),
              ],
            ),
            child: Icon(Icons.abc, color: Colors.white,))
          // IconButton(
          //   on: () async {
          //     showMenu(context: context, items: items)
          //   },
          //   icon: const Icon(Icons.close),
          // ),
        ],),
      backgroundColor: Colors.transparent,
      body: ValueListenableBuilder(
        valueListenable: PreviewImagePage.listMedia,
        builder: (final context, final vListAttachment, final child) {
          return Theme(
            data: ThemeData(
              // ignore: deprecated_member_use
              dialogBackgroundColor: Colors.black,
            ),
            child: ExtendedImageSlidePage(
              slideAxis: SlideAxis.vertical,
              slideType: SlideType.wholePage,
              child: ExtendedImageGesturePageView.builder(
                controller: ExtendedPageController(
                  initialPage: widget.initIndex,
                ),
                onPageChanged: (final val) {
                  StoryImage.currentIdx = val;
                },
                itemCount: vListAttachment.length,
                itemBuilder: (final context, final index) =>
                    vListAttachment[index],
              ),
            ),
          );
        },
      ),
    );
  }
}
