import 'package:json_annotation/json_annotation.dart';

part 'image_by_combo_tag_params.g.dart';

@JsonSerializable(createFactory: false, includeIfNull: false)
class ImageByComboTagQueryParams {
  ImageByComboTagQueryParams({this.itemGroupIds, this.tagIds,
   this.pageNumber, this.pageSize = 5});

  final String? itemGroupIds;

  final String? tagIds;

  final int? pageNumber;

  final int? pageSize;

  Map<String, dynamic> toJson() => _$ImageByComboTagQueryParamsToJson(this);
}
