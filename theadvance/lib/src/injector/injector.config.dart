// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:flutter_cache_manager/flutter_cache_manager.dart' as _i8;
import 'package:get_it/get_it.dart' as _i1;
import 'package:injectable/injectable.dart' as _i2;
import 'package:theadvance/src/core/network/ez_network.dart' as _i11;
import 'package:theadvance/src/core/routes/app_router.dart' as _i10;
import 'package:theadvance/src/core/utils/deeplink_helper.dart' as _i7;
import 'package:theadvance/src/core/utils/mappers.dart' as _i6;
import 'package:theadvance/src/core/utils/stringee_helper.dart' as _i5;
import 'package:theadvance/src/data/datasources/ez_datasources.dart' as _i9;
import 'package:theadvance/src/data/datasources/local/assign_task/assign_task_dao.dart'
    as _i78;
import 'package:theadvance/src/data/datasources/local/assign_task/assign_task_dao_impl.dart'
    as _i79;
import 'package:theadvance/src/data/datasources/local/branch_chat_list/branch_chat_list_dao.dart'
    as _i60;
import 'package:theadvance/src/data/datasources/local/branch_chat_list/branch_chat_list_dao_impl.dart'
    as _i61;
import 'package:theadvance/src/data/datasources/local/branch_selection/branch_selection_dao.dart'
    as _i42;
import 'package:theadvance/src/data/datasources/local/branch_selection/branch_selection_dao_impl.dart'
    as _i43;
import 'package:theadvance/src/data/datasources/local/cache/hive/ez_cache.dart'
    as _i23;
import 'package:theadvance/src/data/datasources/local/chat/chat_dao.dart'
    as _i148;
import 'package:theadvance/src/data/datasources/local/chat/chat_dao_impl.dart'
    as _i149;
import 'package:theadvance/src/data/datasources/local/chat_list/chat_list_dao.dart'
    as _i92;
import 'package:theadvance/src/data/datasources/local/chat_list/chat_list_dao_impl.dart'
    as _i93;
import 'package:theadvance/src/data/datasources/local/chat_select_branch/chat_select_branch_dao.dart'
    as _i112;
import 'package:theadvance/src/data/datasources/local/chat_select_branch/chat_select_branch_dao_impl.dart'
    as _i113;
import 'package:theadvance/src/data/datasources/local/checkin_photo/checkin_photo_dao.dart'
    as _i28;
import 'package:theadvance/src/data/datasources/local/checkin_photo/checkin_photo_dao_impl.dart'
    as _i29;
import 'package:theadvance/src/data/datasources/local/consultation_customer/consultation_customer_dao.dart'
    as _i80;
import 'package:theadvance/src/data/datasources/local/consultation_customer/consultation_customer_dao_impl.dart'
    as _i81;
import 'package:theadvance/src/data/datasources/local/consultation_manager/consultation_manager_dao.dart'
    as _i94;
import 'package:theadvance/src/data/datasources/local/consultation_manager/consultation_manager_dao_impl.dart'
    as _i95;
import 'package:theadvance/src/data/datasources/local/create_chat_folder/create_chat_folder_dao.dart'
    as _i62;
import 'package:theadvance/src/data/datasources/local/create_chat_folder/create_chat_folder_dao_impl.dart'
    as _i63;
import 'package:theadvance/src/data/datasources/local/create_chat_group/create_chat_group_dao.dart'
    as _i102;
import 'package:theadvance/src/data/datasources/local/create_chat_group/create_chat_group_dao_impl.dart'
    as _i103;
import 'package:theadvance/src/data/datasources/local/create_customer/create_customer_dao.dart'
    as _i96;
import 'package:theadvance/src/data/datasources/local/create_customer/create_customer_dao_impl.dart'
    as _i97;
import 'package:theadvance/src/data/datasources/local/customer_booking_info/customer_booking_info_dao.dart'
    as _i98;
import 'package:theadvance/src/data/datasources/local/customer_booking_info/customer_booking_info_dao_impl.dart'
    as _i99;
import 'package:theadvance/src/data/datasources/local/customer_info_details/customer_info_details_dao.dart'
    as _i132;
import 'package:theadvance/src/data/datasources/local/customer_info_details/customer_info_details_dao_impl.dart'
    as _i133;
import 'package:theadvance/src/data/datasources/local/customer_list/customer_list_dao.dart'
    as _i52;
import 'package:theadvance/src/data/datasources/local/customer_list/customer_list_dao_impl.dart'
    as _i53;
import 'package:theadvance/src/data/datasources/local/customer_profile/customer_profile_dao.dart'
    as _i90;
import 'package:theadvance/src/data/datasources/local/customer_profile/customer_profile_dao_impl.dart'
    as _i91;
import 'package:theadvance/src/data/datasources/local/customer_record/customer_record_dao.dart'
    as _i108;
import 'package:theadvance/src/data/datasources/local/customer_record/customer_record_dao_impl.dart'
    as _i109;
import 'package:theadvance/src/data/datasources/local/customer_schedule/customer_schedule_dao.dart'
    as _i56;
import 'package:theadvance/src/data/datasources/local/customer_schedule/customer_schedule_dao_impl.dart'
    as _i57;
import 'package:theadvance/src/data/datasources/local/detail_crm_customer/detail_crm_customer_dao.dart'
    as _i58;
import 'package:theadvance/src/data/datasources/local/detail_crm_customer/detail_crm_customer_dao_impl.dart'
    as _i59;
import 'package:theadvance/src/data/datasources/local/detail_staff_evaluation_period/detail_staff_evaluation_period_dao.dart'
    as _i138;
import 'package:theadvance/src/data/datasources/local/detail_staff_evaluation_period/detail_staff_evaluation_period_dao_impl.dart'
    as _i139;
import 'package:theadvance/src/data/datasources/local/dev/dev_dao.dart' as _i84;
import 'package:theadvance/src/data/datasources/local/dev/dev_dao_impl.dart'
    as _i85;
import 'package:theadvance/src/data/datasources/local/feedback/feedback_dao.dart'
    as _i64;
import 'package:theadvance/src/data/datasources/local/feedback/feedback_dao_impl.dart'
    as _i65;
import 'package:theadvance/src/data/datasources/local/group_chat_detail/group_chat_detail_dao.dart'
    as _i26;
import 'package:theadvance/src/data/datasources/local/group_chat_detail/group_chat_detail_dao_impl.dart'
    as _i27;
import 'package:theadvance/src/data/datasources/local/hr_organization/hr_organization_dao.dart'
    as _i34;
import 'package:theadvance/src/data/datasources/local/hr_organization/hr_organization_dao_impl.dart'
    as _i35;
import 'package:theadvance/src/data/datasources/local/important_notes/important_notes_dao.dart'
    as _i118;
import 'package:theadvance/src/data/datasources/local/important_notes/important_notes_dao_impl.dart'
    as _i119;
import 'package:theadvance/src/data/datasources/local/list_customer/list_customer_dao.dart'
    as _i106;
import 'package:theadvance/src/data/datasources/local/list_customer/list_customer_dao_impl.dart'
    as _i107;
import 'package:theadvance/src/data/datasources/local/location_google/location_google_dao.dart'
    as _i24;
import 'package:theadvance/src/data/datasources/local/location_google/location_google_dao_impl.dart'
    as _i25;
import 'package:theadvance/src/data/datasources/local/medical_department_list/medical_department_list_dao.dart'
    as _i86;
import 'package:theadvance/src/data/datasources/local/medical_department_list/medical_department_list_dao_impl.dart'
    as _i87;
import 'package:theadvance/src/data/datasources/local/medical_log_detail/medical_log_detail_dao.dart'
    as _i30;
import 'package:theadvance/src/data/datasources/local/medical_log_detail/medical_log_detail_dao_impl.dart'
    as _i31;
import 'package:theadvance/src/data/datasources/local/medical_product_creation/medical_product_creation_dao.dart'
    as _i50;
import 'package:theadvance/src/data/datasources/local/medical_product_creation/medical_product_creation_dao_impl.dart'
    as _i51;
import 'package:theadvance/src/data/datasources/local/medical_service_creation/medical_service_creation_dao.dart'
    as _i19;
import 'package:theadvance/src/data/datasources/local/medical_service_creation/medical_service_creation_dao_impl.dart'
    as _i20;
import 'package:theadvance/src/data/datasources/local/medical_service_list/medical_service_list_dao.dart'
    as _i82;
import 'package:theadvance/src/data/datasources/local/medical_service_list/medical_service_list_dao_impl.dart'
    as _i83;
import 'package:theadvance/src/data/datasources/local/medical_service_log_list/medical_service_log_list_dao.dart'
    as _i54;
import 'package:theadvance/src/data/datasources/local/medical_service_log_list/medical_service_log_list_dao_impl.dart'
    as _i55;
import 'package:theadvance/src/data/datasources/local/medical_template_list/medical_template_list_dao.dart'
    as _i120;
import 'package:theadvance/src/data/datasources/local/medical_template_list/medical_template_list_dao_impl.dart'
    as _i121;
import 'package:theadvance/src/data/datasources/local/medicine_detail/medicine_detail_dao.dart'
    as _i110;
import 'package:theadvance/src/data/datasources/local/medicine_detail/medicine_detail_dao_impl.dart'
    as _i111;
import 'package:theadvance/src/data/datasources/local/note_details/note_details_dao.dart'
    as _i100;
import 'package:theadvance/src/data/datasources/local/note_details/note_details_dao_impl.dart'
    as _i101;
import 'package:theadvance/src/data/datasources/local/notification_list/notification_list_dao.dart'
    as _i134;
import 'package:theadvance/src/data/datasources/local/notification_list/notification_list_dao_impl.dart'
    as _i135;
import 'package:theadvance/src/data/datasources/local/product_confirm/product_confirm_dao.dart'
    as _i104;
import 'package:theadvance/src/data/datasources/local/product_confirm/product_confirm_dao_impl.dart'
    as _i105;
import 'package:theadvance/src/data/datasources/local/px_list/px_list_dao.dart'
    as _i38;
import 'package:theadvance/src/data/datasources/local/px_list/px_list_dao_impl.dart'
    as _i39;
import 'package:theadvance/src/data/datasources/local/px_recheck/px_recheck_dao.dart'
    as _i32;
import 'package:theadvance/src/data/datasources/local/px_recheck/px_recheck_dao_impl.dart'
    as _i33;
import 'package:theadvance/src/data/datasources/local/px_task_list/px_task_list_dao.dart'
    as _i21;
import 'package:theadvance/src/data/datasources/local/px_task_list/px_task_list_dao_impl.dart'
    as _i22;
import 'package:theadvance/src/data/datasources/local/px_unasigned/px_unasigned_dao.dart'
    as _i48;
import 'package:theadvance/src/data/datasources/local/px_unasigned/px_unasigned_dao_impl.dart'
    as _i49;
import 'package:theadvance/src/data/datasources/local/px_unasigned_update/px_unasigned_update_dao.dart'
    as _i76;
import 'package:theadvance/src/data/datasources/local/px_unasigned_update/px_unasigned_update_dao_impl.dart'
    as _i77;
import 'package:theadvance/src/data/datasources/local/schedule_details/schedule_details_dao.dart'
    as _i116;
import 'package:theadvance/src/data/datasources/local/schedule_details/schedule_details_dao_impl.dart'
    as _i117;
import 'package:theadvance/src/data/datasources/local/select_px_room/select_px_room_dao.dart'
    as _i130;
import 'package:theadvance/src/data/datasources/local/select_px_room/select_px_room_dao_impl.dart'
    as _i131;
import 'package:theadvance/src/data/datasources/local/service_and_product/service_and_product_dao.dart'
    as _i46;
import 'package:theadvance/src/data/datasources/local/service_and_product/service_and_product_dao_impl.dart'
    as _i47;
import 'package:theadvance/src/data/datasources/local/staff_evaluation_periods/staff_evaluation_periods_dao.dart'
    as _i136;
import 'package:theadvance/src/data/datasources/local/staff_evaluation_periods/staff_evaluation_periods_dao_impl.dart'
    as _i137;
import 'package:theadvance/src/data/datasources/local/story_detail/story_detail_dao.dart'
    as _i114;
import 'package:theadvance/src/data/datasources/local/story_detail/story_detail_dao_impl.dart'
    as _i115;
import 'package:theadvance/src/data/datasources/local/tag_list/tag_list_dao.dart'
    as _i12;
import 'package:theadvance/src/data/datasources/local/tag_list/tag_list_dao_impl.dart'
    as _i13;
import 'package:theadvance/src/data/datasources/local/taking_care_customer/taking_care_customer_dao.dart'
    as _i36;
import 'package:theadvance/src/data/datasources/local/taking_care_customer/taking_care_customer_dao_impl.dart'
    as _i37;
import 'package:theadvance/src/data/datasources/local/ticket_detail/ticket_detail_dao.dart'
    as _i146;
import 'package:theadvance/src/data/datasources/local/ticket_detail/ticket_detail_dao_impl.dart'
    as _i147;
import 'package:theadvance/src/data/datasources/local/user_list/user_list_dao.dart'
    as _i126;
import 'package:theadvance/src/data/datasources/local/user_list/user_list_dao_impl.dart'
    as _i127;
import 'package:theadvance/src/data/datasources/remote/api_services.dart'
    as _i18;
import 'package:theadvance/src/data/datasources/remote/chat_api_service.dart'
    as _i143;
import 'package:theadvance/src/data/datasources/remote/comment_list_api_service.dart'
    as _i15;
import 'package:theadvance/src/data/datasources/remote/create_chat_group_api_service.dart'
    as _i187;
import 'package:theadvance/src/data/datasources/remote/like_list_api_service.dart'
    as _i16;
import 'package:theadvance/src/data/datasources/remote/media_upload_api_service.dart'
    as _i142;
import 'package:theadvance/src/data/datasources/remote/note_list_api_service.dart'
    as _i186;
import 'package:theadvance/src/data/datasources/remote/rating_human_api_service.dart'
    as _i185;
import 'package:theadvance/src/data/datasources/remote/schedule_details_api_service.dart'
    as _i428;
import 'package:theadvance/src/data/datasources/remote/social_upload_api_service.dart'
    as _i144;
import 'package:theadvance/src/data/datasources/remote/sticker_social_api_service.dart'
    as _i145;
import 'package:theadvance/src/data/datasources/remote/story_list_api_service.dart'
    as _i14;
import 'package:theadvance/src/data/datasources/remote/story_person_list_api_service.dart'
    as _i17;
import 'package:theadvance/src/data/datasources/remote/tag_image_service.dart'
    as _i190;
import 'package:theadvance/src/data/datasources/remote/ticket_active_api_service.dart'
    as _i189;
import 'package:theadvance/src/data/datasources/remote/ticket_api_service.dart'
    as _i188;
import 'package:theadvance/src/data/repositories/assign_task_repository_impl.dart'
    as _i639;
import 'package:theadvance/src/data/repositories/branch_chat_list_repository_impl.dart'
    as _i417;
import 'package:theadvance/src/data/repositories/branch_selection_repository_impl.dart'
    as _i198;
import 'package:theadvance/src/data/repositories/chat_list_repository_impl.dart'
    as _i41;
import 'package:theadvance/src/data/repositories/chat_repository_impl.dart'
    as _i177;
import 'package:theadvance/src/data/repositories/chat_select_branch_repository_impl.dart'
    as _i332;
import 'package:theadvance/src/data/repositories/checkin_photo_repository_impl.dart'
    as _i252;
import 'package:theadvance/src/data/repositories/checkin_repository_impl.dart'
    as _i394;
import 'package:theadvance/src/data/repositories/comment_list_repository_impl.dart'
    as _i467;
import 'package:theadvance/src/data/repositories/consultation_customer_repository_impl.dart'
    as _i268;
import 'package:theadvance/src/data/repositories/consultation_manager_repository_impl.dart'
    as _i630;
import 'package:theadvance/src/data/repositories/create_chat_folder_repository_impl.dart'
    as _i125;
import 'package:theadvance/src/data/repositories/create_chat_group_repository_impl.dart'
    as _i45;
import 'package:theadvance/src/data/repositories/create_customer_repository_impl.dart'
    as _i392;
import 'package:theadvance/src/data/repositories/customer_booking_info_repository_impl.dart'
    as _i379;
import 'package:theadvance/src/data/repositories/customer_info_details_repository_impl.dart'
    as _i563;
import 'package:theadvance/src/data/repositories/customer_list_repository_impl.dart'
    as _i381;
import 'package:theadvance/src/data/repositories/customer_profile_repository_impl.dart'
    as _i402;
import 'package:theadvance/src/data/repositories/customer_record_repository_impl.dart'
    as _i322;
import 'package:theadvance/src/data/repositories/customer_repository_impl.dart'
    as _i290;
import 'package:theadvance/src/data/repositories/customer_schedule_repository_impl.dart'
    as _i514;
import 'package:theadvance/src/data/repositories/detail_crm_customer_repository_impl.dart'
    as _i369;
import 'package:theadvance/src/data/repositories/detail_staff_evaluation_period_repository_impl.dart'
    as _i204;
import 'package:theadvance/src/data/repositories/dev_repository_impl.dart'
    as _i348;
import 'package:theadvance/src/data/repositories/eform_repository_impl.dart'
    as _i486;
import 'package:theadvance/src/data/repositories/feedback_repository_impl.dart'
    as _i271;
import 'package:theadvance/src/data/repositories/food_repository_impl.dart'
    as _i196;
import 'package:theadvance/src/data/repositories/group_chat_detail_repository_impl.dart'
    as _i171;
import 'package:theadvance/src/data/repositories/helper_repository_impl.dart'
    as _i129;
import 'package:theadvance/src/data/repositories/home_repository_impl.dart'
    as _i453;
import 'package:theadvance/src/data/repositories/hr_organization_repository_impl.dart'
    as _i565;
import 'package:theadvance/src/data/repositories/important_notes_repository_impl.dart'
    as _i206;
import 'package:theadvance/src/data/repositories/kpi_employee_repository_impl.dart'
    as _i516;
import 'package:theadvance/src/data/repositories/like_list_repository_impl.dart'
    as _i194;
import 'package:theadvance/src/data/repositories/list_customer_repository_impl.dart'
    as _i424;
import 'package:theadvance/src/data/repositories/location_google_repository_impl.dart'
    as _i350;
import 'package:theadvance/src/data/repositories/media_upload_repository_impl.dart'
    as _i377;
import 'package:theadvance/src/data/repositories/medical_department_list_repository_impl.dart'
    as _i202;
import 'package:theadvance/src/data/repositories/medical_log_detail_repository_impl.dart'
    as _i507;
import 'package:theadvance/src/data/repositories/medical_product_create_repository_impl.dart'
    as _i451;
import 'package:theadvance/src/data/repositories/medical_service_create_repository_impl.dart'
    as _i687;
import 'package:theadvance/src/data/repositories/medical_service_list_repository_impl.dart'
    as _i364;
import 'package:theadvance/src/data/repositories/medical_service_log_list_repository_impl.dart'
    as _i641;
import 'package:theadvance/src/data/repositories/medical_template_list_repository_impl.dart'
    as _i663;
import 'package:theadvance/src/data/repositories/medicine_detail_repository_impl.dart'
    as _i210;
import 'package:theadvance/src/data/repositories/news_repository_impl.dart'
    as _i390;
import 'package:theadvance/src/data/repositories/note_details_repository_impl.dart'
    as _i414;
import 'package:theadvance/src/data/repositories/notification_list_repository_impl.dart'
    as _i89;
import 'package:theadvance/src/data/repositories/notification_repository_impl.dart'
    as _i484;
import 'package:theadvance/src/data/repositories/product_confirm_repository_impl.dart'
    as _i366;
import 'package:theadvance/src/data/repositories/px_list_repository_impl.dart'
    as _i344;
import 'package:theadvance/src/data/repositories/px_recheck_repository_impl.dart'
    as _i383;
import 'package:theadvance/src/data/repositories/px_task_list_repository_impl.dart'
    as _i254;
import 'package:theadvance/src/data/repositories/px_unasigned_repository_impl.dart'
    as _i441;
import 'package:theadvance/src/data/repositories/px_unasigned_update_repository_impl.dart'
    as _i548;
import 'package:theadvance/src/data/repositories/rating_human_repository_impl.dart'
    as _i258;
import 'package:theadvance/src/data/repositories/request_repository_impl.dart'
    as _i677;
import 'package:theadvance/src/data/repositories/schedule_details_repository_impl.dart'
    as _i427;
import 'package:theadvance/src/data/repositories/select_px_room_repository_impl.dart'
    as _i192;
import 'package:theadvance/src/data/repositories/service_and_product_repository_impl.dart'
    as _i498;
import 'package:theadvance/src/data/repositories/setting_repository_impl.dart'
    as _i141;
import 'package:theadvance/src/data/repositories/staff_evaluation_periods_repository_impl.dart'
    as _i439;
import 'package:theadvance/src/data/repositories/staff_repository_impl.dart'
    as _i445;
import 'package:theadvance/src/data/repositories/sticker_social_repository_impl.dart'
    as _i184;
import 'package:theadvance/src/data/repositories/story_detail_repository_impl.dart'
    as _i123;
import 'package:theadvance/src/data/repositories/story_list_repository_impl.dart'
    as _i169;
import 'package:theadvance/src/data/repositories/story_person_list_repository_impl.dart'
    as _i167;
import 'package:theadvance/src/data/repositories/tag_image_repository_impl.dart'
    as _i689;
import 'package:theadvance/src/data/repositories/tag_list_repository_impl.dart'
    as _i151;
import 'package:theadvance/src/data/repositories/taking_care_customer_repository_impl.dart'
    as _i371;
import 'package:theadvance/src/data/repositories/task_repository_impl.dart'
    as _i346;
import 'package:theadvance/src/data/repositories/ticket_active_repository_impl.dart'
    as _i200;
import 'package:theadvance/src/data/repositories/ticket_detail_repository_impl.dart'
    as _i385;
import 'package:theadvance/src/data/repositories/ticketv2_repository_impl.dart'
    as _i256;
import 'package:theadvance/src/data/repositories/user_list_repository_impl.dart'
    as _i550;
import 'package:theadvance/src/data/repositories/user_repository_impl.dart'
    as _i266;
import 'package:theadvance/src/data/repositories/user_ticket_repository_impl.dart'
    as _i208;
import 'package:theadvance/src/domain/repositories/assign_task_repository.dart'
    as _i638;
import 'package:theadvance/src/domain/repositories/branch_chat_list_repository.dart'
    as _i416;
import 'package:theadvance/src/domain/repositories/branch_selection_repository.dart'
    as _i197;
import 'package:theadvance/src/domain/repositories/chat_list_repository.dart'
    as _i40;
import 'package:theadvance/src/domain/repositories/chat_repository.dart'
    as _i176;
import 'package:theadvance/src/domain/repositories/chat_select_branch_repository.dart'
    as _i331;
import 'package:theadvance/src/domain/repositories/checkin_photo_repository.dart'
    as _i251;
import 'package:theadvance/src/domain/repositories/checkin_repository.dart'
    as _i393;
import 'package:theadvance/src/domain/repositories/comment_list_repository.dart'
    as _i466;
import 'package:theadvance/src/domain/repositories/consultation_customer_repository.dart'
    as _i267;
import 'package:theadvance/src/domain/repositories/consultation_manager_repository.dart'
    as _i629;
import 'package:theadvance/src/domain/repositories/create_chat_folder_repository.dart'
    as _i124;
import 'package:theadvance/src/domain/repositories/create_chat_group_repository.dart'
    as _i44;
import 'package:theadvance/src/domain/repositories/create_customer_repository.dart'
    as _i391;
import 'package:theadvance/src/domain/repositories/customer_booking_info_repository.dart'
    as _i378;
import 'package:theadvance/src/domain/repositories/customer_info_details_repository.dart'
    as _i562;
import 'package:theadvance/src/domain/repositories/customer_list_repository.dart'
    as _i380;
import 'package:theadvance/src/domain/repositories/customer_profile_repository.dart'
    as _i401;
import 'package:theadvance/src/domain/repositories/customer_record_repository.dart'
    as _i321;
import 'package:theadvance/src/domain/repositories/customer_repository.dart'
    as _i289;
import 'package:theadvance/src/domain/repositories/customer_schedule_repository.dart'
    as _i513;
import 'package:theadvance/src/domain/repositories/detail_crm_customer_repository.dart'
    as _i368;
import 'package:theadvance/src/domain/repositories/detail_staff_evaluation_period_repository.dart'
    as _i203;
import 'package:theadvance/src/domain/repositories/dev_repository.dart'
    as _i347;
import 'package:theadvance/src/domain/repositories/eform_repository.dart'
    as _i485;
import 'package:theadvance/src/domain/repositories/feedback_repository.dart'
    as _i270;
import 'package:theadvance/src/domain/repositories/food_repository.dart'
    as _i195;
import 'package:theadvance/src/domain/repositories/group_chat_detail_repository.dart'
    as _i170;
import 'package:theadvance/src/domain/repositories/helper_repository.dart'
    as _i128;
import 'package:theadvance/src/domain/repositories/home_repository.dart'
    as _i452;
import 'package:theadvance/src/domain/repositories/hr_organization_repository.dart'
    as _i564;
import 'package:theadvance/src/domain/repositories/important_notes_repository.dart'
    as _i205;
import 'package:theadvance/src/domain/repositories/kpi_employee_repository.dart'
    as _i515;
import 'package:theadvance/src/domain/repositories/like_list_repository.dart'
    as _i193;
import 'package:theadvance/src/domain/repositories/list_customer_repository.dart'
    as _i423;
import 'package:theadvance/src/domain/repositories/location_google_repository.dart'
    as _i349;
import 'package:theadvance/src/domain/repositories/media_upload_repository.dart'
    as _i376;
import 'package:theadvance/src/domain/repositories/medical_department_list_repository.dart'
    as _i201;
import 'package:theadvance/src/domain/repositories/medical_log_detail_repository.dart'
    as _i506;
import 'package:theadvance/src/domain/repositories/medical_product_create_repository.dart'
    as _i450;
import 'package:theadvance/src/domain/repositories/medical_service_create_repository.dart'
    as _i686;
import 'package:theadvance/src/domain/repositories/medical_service_list_repository.dart'
    as _i363;
import 'package:theadvance/src/domain/repositories/medical_service_log_list_repository.dart'
    as _i640;
import 'package:theadvance/src/domain/repositories/medical_template_list_repository.dart'
    as _i662;
import 'package:theadvance/src/domain/repositories/medicine_detail_repository.dart'
    as _i209;
import 'package:theadvance/src/domain/repositories/news_repository.dart'
    as _i389;
import 'package:theadvance/src/domain/repositories/note_details_repository.dart'
    as _i413;
import 'package:theadvance/src/domain/repositories/notification_list_repository.dart'
    as _i88;
import 'package:theadvance/src/domain/repositories/notification_repository.dart'
    as _i483;
import 'package:theadvance/src/domain/repositories/product_confirm_repository.dart'
    as _i365;
import 'package:theadvance/src/domain/repositories/px_list_repository.dart'
    as _i343;
import 'package:theadvance/src/domain/repositories/px_recheck_repository.dart'
    as _i382;
import 'package:theadvance/src/domain/repositories/px_task_list_repository.dart'
    as _i253;
import 'package:theadvance/src/domain/repositories/px_unasigned_repository.dart'
    as _i440;
import 'package:theadvance/src/domain/repositories/px_unasigned_update_repository.dart'
    as _i547;
import 'package:theadvance/src/domain/repositories/rating_human_repository.dart'
    as _i257;
import 'package:theadvance/src/domain/repositories/request_repository.dart'
    as _i676;
import 'package:theadvance/src/domain/repositories/schedule_details_repository.dart'
    as _i426;
import 'package:theadvance/src/domain/repositories/select_px_room_repository.dart'
    as _i191;
import 'package:theadvance/src/domain/repositories/service_and_product_repository.dart'
    as _i497;
import 'package:theadvance/src/domain/repositories/setting_repository.dart'
    as _i140;
import 'package:theadvance/src/domain/repositories/staff_evaluation_periods_repository.dart'
    as _i438;
import 'package:theadvance/src/domain/repositories/staff_repository.dart'
    as _i444;
import 'package:theadvance/src/domain/repositories/sticker_social_repository.dart'
    as _i183;
import 'package:theadvance/src/domain/repositories/story_detail_repository.dart'
    as _i122;
import 'package:theadvance/src/domain/repositories/story_list_repository.dart'
    as _i168;
import 'package:theadvance/src/domain/repositories/story_person_list_repository.dart'
    as _i166;
import 'package:theadvance/src/domain/repositories/tag_image_repository.dart'
    as _i688;
import 'package:theadvance/src/domain/repositories/tag_list_repository.dart'
    as _i150;
import 'package:theadvance/src/domain/repositories/taking_care_customer_repository.dart'
    as _i370;
import 'package:theadvance/src/domain/repositories/task_repository.dart'
    as _i345;
import 'package:theadvance/src/domain/repositories/ticket_active_repository.dart'
    as _i199;
import 'package:theadvance/src/domain/repositories/ticket_detail_repository.dart'
    as _i384;
import 'package:theadvance/src/domain/repositories/ticketv2_repository.dart'
    as _i255;
import 'package:theadvance/src/domain/repositories/tracking_repository.dart'
    as _i67;
import 'package:theadvance/src/domain/repositories/user_list_repository.dart'
    as _i549;
import 'package:theadvance/src/domain/repositories/user_repository.dart'
    as _i265;
import 'package:theadvance/src/domain/repositories/user_ticker_repository.dart'
    as _i207;
import 'package:theadvance/src/domain/usecases/assign_task/create_assign_task_usecase.dart'
    as _i947;
import 'package:theadvance/src/domain/usecases/assign_task/delete_assign_task_usecase.dart'
    as _i944;
import 'package:theadvance/src/domain/usecases/assign_task/get_assign_task_usecase.dart'
    as _i949;
import 'package:theadvance/src/domain/usecases/assign_task/get_saved_assign_task_usecase.dart'
    as _i946;
import 'package:theadvance/src/domain/usecases/assign_task/get_staff_assign_task_usecase.dart'
    as _i951;
import 'package:theadvance/src/domain/usecases/assign_task/remove_assign_task_usecase.dart'
    as _i950;
import 'package:theadvance/src/domain/usecases/assign_task/save_assign_task_usecase.dart'
    as _i948;
import 'package:theadvance/src/domain/usecases/assign_task/update_assign_task_usecase.dart'
    as _i945;
import 'package:theadvance/src/domain/usecases/branch_chat_list/get_branch_chat_list_usecase.dart'
    as _i672;
import 'package:theadvance/src/domain/usecases/branch_chat_list/get_saved_branch_chat_list_usecase.dart'
    as _i673;
import 'package:theadvance/src/domain/usecases/branch_chat_list/remove_branch_chat_list_usecase.dart'
    as _i674;
import 'package:theadvance/src/domain/usecases/branch_chat_list/save_branch_chat_list_usecase.dart'
    as _i675;
import 'package:theadvance/src/domain/usecases/branch_selection/bed_change_branch_selection_usecase.dart'
    as _i622;
import 'package:theadvance/src/domain/usecases/branch_selection/bed_select_branch_selection_usecase.dart'
    as _i620;
import 'package:theadvance/src/domain/usecases/branch_selection/employee_get_branch_selection_usecase.dart'
    as _i617;
import 'package:theadvance/src/domain/usecases/branch_selection/estimate_time_get_branch_selection_usecase.dart'
    as _i623;
import 'package:theadvance/src/domain/usecases/branch_selection/get_bed_branch_selection_usecase.dart'
    as _i625;
import 'package:theadvance/src/domain/usecases/branch_selection/get_branch_selection_usecase.dart'
    as _i626;
import 'package:theadvance/src/domain/usecases/branch_selection/get_floor_branch_selection_usecase.dart'
    as _i621;
import 'package:theadvance/src/domain/usecases/branch_selection/get_room_branch_selection_usecase.dart'
    as _i627;
import 'package:theadvance/src/domain/usecases/branch_selection/get_saved_branch_selection_usecase.dart'
    as _i624;
import 'package:theadvance/src/domain/usecases/branch_selection/remove_branch_selection_usecase.dart'
    as _i618;
import 'package:theadvance/src/domain/usecases/branch_selection/save_branch_selection_usecase.dart'
    as _i619;
import 'package:theadvance/src/domain/usecases/chat/conversation_details_update_chat_usecase.dart'
    as _i232;
import 'package:theadvance/src/domain/usecases/chat/get_chat_usecase.dart'
    as _i229;
import 'package:theadvance/src/domain/usecases/chat/get_conversation_by_id_usecase.dart'
    as _i225;
import 'package:theadvance/src/domain/usecases/chat/get_conversation_chat_usecase.dart'
    as _i216;
import 'package:theadvance/src/domain/usecases/chat/get_pin_list_chat_usecase.dart'
    as _i217;
import 'package:theadvance/src/domain/usecases/chat/get_saved_chat_usecase.dart'
    as _i211;
import 'package:theadvance/src/domain/usecases/chat/get_user_seen_chat_usecase.dart'
    as _i213;
import 'package:theadvance/src/domain/usecases/chat/get_user_sticker_chat_usecase.dart'
    as _i220;
import 'package:theadvance/src/domain/usecases/chat/message_edit_chat_usecase.dart'
    as _i212;
import 'package:theadvance/src/domain/usecases/chat/message_remove_chat_usecase.dart'
    as _i230;
import 'package:theadvance/src/domain/usecases/chat/pin_message_chat_usecase.dart'
    as _i227;
import 'package:theadvance/src/domain/usecases/chat/react_chat_usecase.dart'
    as _i224;
import 'package:theadvance/src/domain/usecases/chat/remove_chat_usecase.dart'
    as _i231;
import 'package:theadvance/src/domain/usecases/chat/reply_bot_message_chat_usecase.dart'
    as _i223;
import 'package:theadvance/src/domain/usecases/chat/save_chat_usecase.dart'
    as _i218;
import 'package:theadvance/src/domain/usecases/chat/search_chat_usecase.dart'
    as _i215;
import 'package:theadvance/src/domain/usecases/chat/send_chat_usecase.dart'
    as _i226;
import 'package:theadvance/src/domain/usecases/chat/transcribe_chat_usecase.dart'
    as _i214;
import 'package:theadvance/src/domain/usecases/chat/unpin_message_chat_usecase.dart'
    as _i222;
import 'package:theadvance/src/domain/usecases/chat/update_poll_chat_usecase.dart'
    as _i228;
import 'package:theadvance/src/domain/usecases/chat/upload_file_chat_usecase.dart'
    as _i221;
import 'package:theadvance/src/domain/usecases/chat/vote_poll_chat_usecase.dart'
    as _i219;
import 'package:theadvance/src/domain/usecases/chat_list/get_chat_list_usecase.dart'
    as _i162;
import 'package:theadvance/src/domain/usecases/chat_list/get_conversation_by_invite_id_chat_list_usecase.dart'
    as _i160;
import 'package:theadvance/src/domain/usecases/chat_list/get_recent_contacts_chat_list_usecase.dart'
    as _i161;
import 'package:theadvance/src/domain/usecases/chat_list/get_saved_chat_list_usecase.dart'
    as _i155;
import 'package:theadvance/src/domain/usecases/chat_list/get_total_unread_chat_list_usecase.dart'
    as _i154;
import 'package:theadvance/src/domain/usecases/chat_list/join_group_chat_list_usecase.dart'
    as _i153;
import 'package:theadvance/src/domain/usecases/chat_list/mark_as_read_chat_list_usecase.dart'
    as _i158;
import 'package:theadvance/src/domain/usecases/chat_list/pin_conversation_chat_list_usecase.dart'
    as _i156;
import 'package:theadvance/src/domain/usecases/chat_list/remove_chat_list_usecase.dart'
    as _i164;
import 'package:theadvance/src/domain/usecases/chat_list/save_chat_list_usecase.dart'
    as _i165;
import 'package:theadvance/src/domain/usecases/chat_list/search_chat_list_usecase.dart'
    as _i157;
import 'package:theadvance/src/domain/usecases/chat_list/search_message_chat_list_usecase.dart'
    as _i159;
import 'package:theadvance/src/domain/usecases/chat_list/sort_folder_chat_list_usecase.dart'
    as _i152;
import 'package:theadvance/src/domain/usecases/chat_list/update_pin_conversation_chat_list_usecase.dart'
    as _i163;
import 'package:theadvance/src/domain/usecases/chat_select_branch/get_chat_select_branch_usecase.dart'
    as _i476;
import 'package:theadvance/src/domain/usecases/chat_select_branch/get_saved_chat_select_branch_usecase.dart'
    as _i473;
import 'package:theadvance/src/domain/usecases/chat_select_branch/remove_chat_select_branch_usecase.dart'
    as _i474;
import 'package:theadvance/src/domain/usecases/chat_select_branch/save_chat_select_branch_usecase.dart'
    as _i475;
import 'package:theadvance/src/domain/usecases/checkin/cache_checkin_hour_get_usecase.dart'
    as _i783;
import 'package:theadvance/src/domain/usecases/checkin/cache_checkin_hour_save_usecase.dart'
    as _i781;
import 'package:theadvance/src/domain/usecases/checkin/cache_checkout_hour_get_usecase.dart'
    as _i782;
import 'package:theadvance/src/domain/usecases/checkin/cache_checkout_hour_save_usecase.dart'
    as _i780;
import 'package:theadvance/src/domain/usecases/checkin/get_branches_usecase.dart'
    as _i840;
import 'package:theadvance/src/domain/usecases/checkin/get_checkin_types_usecase.dart'
    as _i841;
import 'package:theadvance/src/domain/usecases/checkin/get_choices_usecase.dart'
    as _i838;
import 'package:theadvance/src/domain/usecases/checkin/get_monthly_history_checkin_usecase.dart'
    as _i842;
import 'package:theadvance/src/domain/usecases/checkin/request_update_history_checkin_usecase.dart'
    as _i839;
import 'package:theadvance/src/domain/usecases/checkin_photo/get_checkin_photo_usecase.dart'
    as _i665;
import 'package:theadvance/src/domain/usecases/checkin_photo/get_saved_checkin_photo_usecase.dart'
    as _i666;
import 'package:theadvance/src/domain/usecases/checkin_photo/remove_checkin_photo_usecase.dart'
    as _i668;
import 'package:theadvance/src/domain/usecases/checkin_photo/save_checkin_photo_usecase.dart'
    as _i667;
import 'package:theadvance/src/domain/usecases/comment/comment_upload_file_usecase.dart'
    as _i833;
import 'package:theadvance/src/domain/usecases/comment/delete_comment_usecase.dart'
    as _i832;
import 'package:theadvance/src/domain/usecases/comment/get_comment_list_usecase.dart'
    as _i830;
import 'package:theadvance/src/domain/usecases/comment/post_comment_usecase.dart'
    as _i831;
import 'package:theadvance/src/domain/usecases/comment/update_comment_usecase.dart'
    as _i664;
import 'package:theadvance/src/domain/usecases/consultation_customer/complete_consultation_customer_usecase.dart'
    as _i315;
import 'package:theadvance/src/domain/usecases/consultation_customer/create_treatment_detail_usecase.dart'
    as _i299;
import 'package:theadvance/src/domain/usecases/consultation_customer/create_treatment_om_detail_usecase.dart'
    as _i291;
import 'package:theadvance/src/domain/usecases/consultation_customer/delete_result_of_fit_usecase.dart'
    as _i292;
import 'package:theadvance/src/domain/usecases/consultation_customer/edit_service_consultation_customer_usecase.dart'
    as _i316;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_action_consultation_customer_usecase.dart'
    as _i312;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_consultation_customer_usecase.dart'
    as _i308;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_consultation_ndtv_usecase.dart'
    as _i294;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_consultation_ttbd_usecase.dart'
    as _i314;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_fit_customer_info_usecase.dart'
    as _i300;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_result_list_of_fit_usecase.dart'
    as _i295;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_result_of_fit_usecase.dart'
    as _i305;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_saved_consultation_customer_usecase.dart'
    as _i306;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_service_consultation_customer_usecase.dart'
    as _i303;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_service_inside_ticket_usecase.dart'
    as _i293;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_service_om_consultation_customer_usecase.dart'
    as _i304;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_service_usage_consultation_customer_usecase.dart'
    as _i302;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_skin_customer_info_usecase.dart'
    as _i296;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_treatment_detail_usecase.dart'
    as _i319;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_treatment_note_usecase.dart'
    as _i301;
import 'package:theadvance/src/domain/usecases/consultation_customer/product_load_consultation_customer_usecase.dart'
    as _i298;
import 'package:theadvance/src/domain/usecases/consultation_customer/remove_consultation_customer_usecase.dart'
    as _i297;
import 'package:theadvance/src/domain/usecases/consultation_customer/remove_service_consultation_customer_usecase.dart'
    as _i318;
import 'package:theadvance/src/domain/usecases/consultation_customer/save_consultation_customer_usecase.dart'
    as _i310;
import 'package:theadvance/src/domain/usecases/consultation_customer/update_fit_customer_info_usecase.dart'
    as _i309;
import 'package:theadvance/src/domain/usecases/consultation_customer/update_result_of_fit_usecase.dart'
    as _i313;
import 'package:theadvance/src/domain/usecases/consultation_customer/update_skin_customer_info_usecase.dart'
    as _i307;
import 'package:theadvance/src/domain/usecases/consultation_customer/update_treatment_detail_usecase.dart'
    as _i317;
import 'package:theadvance/src/domain/usecases/consultation_customer/update_treatment_note_usecase.dart'
    as _i311;
import 'package:theadvance/src/domain/usecases/consultation_manager/assign_update_usecase.dart'
    as _i907;
import 'package:theadvance/src/domain/usecases/consultation_manager/bed_assign_consultation_manager_usecase.dart'
    as _i908;
import 'package:theadvance/src/domain/usecases/consultation_manager/bed_fetch_consultation_manager_usecase.dart'
    as _i910;
import 'package:theadvance/src/domain/usecases/consultation_manager/delete_service_assign_manager_usecase.dart'
    as _i906;
import 'package:theadvance/src/domain/usecases/consultation_manager/delete_service_customer_usecase.dart'
    as _i911;
import 'package:theadvance/src/domain/usecases/consultation_manager/get_consultation_manager_usecase.dart'
    as _i903;
import 'package:theadvance/src/domain/usecases/consultation_manager/get_customer_consultation_manager_usecase.dart'
    as _i912;
import 'package:theadvance/src/domain/usecases/consultation_manager/get_saved_consultation_manager_usecase.dart'
    as _i909;
import 'package:theadvance/src/domain/usecases/consultation_manager/list_fetch_by_staff_consultation_manager_usecase.dart'
    as _i901;
import 'package:theadvance/src/domain/usecases/consultation_manager/remove_consultation_manager_usecase.dart'
    as _i904;
import 'package:theadvance/src/domain/usecases/consultation_manager/room_fetch_consultation_manager_usecase.dart'
    as _i902;
import 'package:theadvance/src/domain/usecases/consultation_manager/save_consultation_manager_usecase.dart'
    as _i905;
import 'package:theadvance/src/domain/usecases/create_chat_folder/conversation_load_create_chat_folder_usecase.dart'
    as _i328;
import 'package:theadvance/src/domain/usecases/create_chat_folder/get_create_chat_folder_usecase.dart'
    as _i326;
import 'package:theadvance/src/domain/usecases/create_chat_folder/get_saved_create_chat_folder_usecase.dart'
    as _i324;
import 'package:theadvance/src/domain/usecases/create_chat_folder/load_create_chat_folder_usecase.dart'
    as _i325;
import 'package:theadvance/src/domain/usecases/create_chat_folder/remove_create_chat_folder_usecase.dart'
    as _i323;
import 'package:theadvance/src/domain/usecases/create_chat_folder/remove_folder_create_chat_folder_usecase.dart'
    as _i330;
import 'package:theadvance/src/domain/usecases/create_chat_folder/save_create_chat_folder_usecase.dart'
    as _i327;
import 'package:theadvance/src/domain/usecases/create_chat_folder/update_create_chat_folder_usecase.dart'
    as _i329;
import 'package:theadvance/src/domain/usecases/create_chat_group/get_create_chat_group_usecase.dart'
    as _i181;
import 'package:theadvance/src/domain/usecases/create_chat_group/get_saved_create_chat_group_usecase.dart'
    as _i178;
import 'package:theadvance/src/domain/usecases/create_chat_group/remove_create_chat_group_usecase.dart'
    as _i182;
import 'package:theadvance/src/domain/usecases/create_chat_group/save_create_chat_group_usecase.dart'
    as _i179;
import 'package:theadvance/src/domain/usecases/create_chat_group/user_load_create_chat_group_usecase.dart'
    as _i180;
import 'package:theadvance/src/domain/usecases/create_customer/customer_search_create_customer_usecase.dart'
    as _i580;
import 'package:theadvance/src/domain/usecases/create_customer/get_create_customer_usecase.dart'
    as _i576;
import 'package:theadvance/src/domain/usecases/create_customer/get_district_create_customer_usecase.dart'
    as _i574;
import 'package:theadvance/src/domain/usecases/create_customer/get_job_create_customer_usecase.dart'
    as _i571;
import 'package:theadvance/src/domain/usecases/create_customer/get_province_create_customer_usecase.dart'
    as _i570;
import 'package:theadvance/src/domain/usecases/create_customer/get_saved_create_customer_usecase.dart'
    as _i577;
import 'package:theadvance/src/domain/usecases/create_customer/get_ward_create_customer_usecase.dart'
    as _i575;
import 'package:theadvance/src/domain/usecases/create_customer/remove_create_customer_usecase.dart'
    as _i572;
import 'package:theadvance/src/domain/usecases/create_customer/save_create_customer_usecase.dart'
    as _i573;
import 'package:theadvance/src/domain/usecases/create_customer/survey_load_create_customer_usecase.dart'
    as _i579;
import 'package:theadvance/src/domain/usecases/create_customer/update_create_customer_usecase.dart'
    as _i578;
import 'package:theadvance/src/domain/usecases/customer/checkin_customer_usecase.dart'
    as _i816;
import 'package:theadvance/src/domain/usecases/customer/get_customer_info_by_qr_usecase.dart'
    as _i503;
import 'package:theadvance/src/domain/usecases/customer/get_customer_info_usecase.dart'
    as _i504;
import 'package:theadvance/src/domain/usecases/customer/get_customer_room_code_usecase.dart'
    as _i505;
import 'package:theadvance/src/domain/usecases/customer/get_room_list_customer_usecase.dart'
    as _i817;
import 'package:theadvance/src/domain/usecases/customer/print_customer_usecase.dart'
    as _i819;
import 'package:theadvance/src/domain/usecases/customer/save_customer_room_code_usecase.dart'
    as _i818;
import 'package:theadvance/src/domain/usecases/customer_booking_info/booked_services_fetch_customer_booking_info_usecase.dart'
    as _i647;
import 'package:theadvance/src/domain/usecases/customer_booking_info/get_customer_booking_info_usecase.dart'
    as _i646;
import 'package:theadvance/src/domain/usecases/customer_booking_info/get_saved_customer_booking_info_usecase.dart'
    as _i648;
import 'package:theadvance/src/domain/usecases/customer_booking_info/remove_customer_booking_info_usecase.dart'
    as _i643;
import 'package:theadvance/src/domain/usecases/customer_booking_info/save_customer_booking_info_usecase.dart'
    as _i645;
import 'package:theadvance/src/domain/usecases/customer_booking_info/service_details_load_customer_booking_info_usecase.dart'
    as _i644;
import 'package:theadvance/src/domain/usecases/customer_booking_info/suggest_services_fetch_customer_booking_info_usecase.dart'
    as _i642;
import 'package:theadvance/src/domain/usecases/customer_booking_info/used_service_fetch_customer_booking_info_usecase.dart'
    as _i649;
import 'package:theadvance/src/domain/usecases/customer_info_details/checkout_customer_info_details_usecase.dart'
    as _i682;
import 'package:theadvance/src/domain/usecases/customer_info_details/get_customer_info_details_usecase.dart'
    as _i681;
import 'package:theadvance/src/domain/usecases/customer_info_details/get_saved_customer_info_details_usecase.dart'
    as _i683;
import 'package:theadvance/src/domain/usecases/customer_info_details/remove_customer_info_details_usecase.dart'
    as _i684;
import 'package:theadvance/src/domain/usecases/customer_info_details/save_customer_info_details_usecase.dart'
    as _i680;
import 'package:theadvance/src/domain/usecases/customer_list/get_customer_list_usecase.dart'
    as _i739;
import 'package:theadvance/src/domain/usecases/customer_list/get_customer_relationship_list_usecase.dart'
    as _i742;
import 'package:theadvance/src/domain/usecases/customer_list/get_saved_customer_list_usecase.dart'
    as _i740;
import 'package:theadvance/src/domain/usecases/customer_list/remove_customer_list_usecase.dart'
    as _i741;
import 'package:theadvance/src/domain/usecases/customer_list/save_customer_list_usecase.dart'
    as _i738;
import 'package:theadvance/src/domain/usecases/customer_profile/create_consultation_customer_profile_usecase.dart'
    as _i692;
import 'package:theadvance/src/domain/usecases/customer_profile/get_consultation_history_customer_profile_usecase.dart'
    as _i695;
import 'package:theadvance/src/domain/usecases/customer_profile/get_customer_profile_usecase.dart'
    as _i696;
import 'package:theadvance/src/domain/usecases/customer_profile/get_saved_customer_profile_usecase.dart'
    as _i697;
import 'package:theadvance/src/domain/usecases/customer_profile/remove_customer_profile_usecase.dart'
    as _i694;
import 'package:theadvance/src/domain/usecases/customer_profile/save_customer_profile_usecase.dart'
    as _i693;
import 'package:theadvance/src/domain/usecases/customer_profile/update_consultation_customer_profile_usecase.dart'
    as _i698;
import 'package:theadvance/src/domain/usecases/customer_record/get_customer_record_usecase.dart'
    as _i448;
import 'package:theadvance/src/domain/usecases/customer_record/get_saved_customer_record_usecase.dart'
    as _i447;
import 'package:theadvance/src/domain/usecases/customer_record/remove_customer_record_usecase.dart'
    as _i446;
import 'package:theadvance/src/domain/usecases/customer_record/save_customer_record_usecase.dart'
    as _i449;
import 'package:theadvance/src/domain/usecases/customer_schedule/get_customer_schedule_usecase.dart'
    as _i714;
import 'package:theadvance/src/domain/usecases/customer_schedule/get_saved_customer_schedule_usecase.dart'
    as _i712;
import 'package:theadvance/src/domain/usecases/customer_schedule/remove_customer_schedule_usecase.dart'
    as _i713;
import 'package:theadvance/src/domain/usecases/customer_schedule/save_customer_schedule_usecase.dart'
    as _i711;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/advice_fetch_detail_crm_customer_usecase.dart'
    as _i530;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/advice_type_fetch_detail_crm_customer_usecase.dart'
    as _i542;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/advice_update_detail_crm_customer_usecase.dart'
    as _i541;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/book_detail_crm_customer_usecase.dart'
    as _i539;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/booking_detail_load_detail_crm_customer_usecase.dart'
    as _i543;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/booking_load_detail_crm_customer_usecase.dart'
    as _i546;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/booking_log_fetch_detail_crm_customer_usecase.dart'
    as _i538;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/branch_load_detail_crm_customer_usecase.dart'
    as _i536;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/call_log_fetch_detail_crm_customer_usecase.dart'
    as _i535;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/get_detail_crm_customer_usecase.dart'
    as _i531;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/get_saved_detail_crm_customer_usecase.dart'
    as _i537;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/message_log_fetch_detail_crm_customer_usecase.dart'
    as _i544;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/number_booking_load_detail_crm_customer_usecase.dart'
    as _i545;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/promotion_load_detail_crm_customer_usecase.dart'
    as _i528;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/remove_detail_crm_customer_usecase.dart'
    as _i534;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/room_load_detail_crm_customer_usecase.dart'
    as _i527;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/save_detail_crm_customer_usecase.dart'
    as _i532;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/service_fetch_detail_crm_customer_usecase.dart'
    as _i529;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/service_load_detail_crm_customer_usecase.dart'
    as _i533;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/time_load_detail_crm_customer_usecase.dart'
    as _i540;
import 'package:theadvance/src/domain/usecases/detail_staff_evaluation_period/employee_fetch_detail_staff_evaluation_period_usecase.dart'
    as _i478;
import 'package:theadvance/src/domain/usecases/detail_staff_evaluation_period/get_detail_staff_evaluation_period_usecase.dart'
    as _i481;
import 'package:theadvance/src/domain/usecases/detail_staff_evaluation_period/get_saved_detail_staff_evaluation_period_usecase.dart'
    as _i480;
import 'package:theadvance/src/domain/usecases/detail_staff_evaluation_period/remove_detail_staff_evaluation_period_usecase.dart'
    as _i479;
import 'package:theadvance/src/domain/usecases/detail_staff_evaluation_period/save_detail_staff_evaluation_period_usecase.dart'
    as _i477;
import 'package:theadvance/src/domain/usecases/dev/get_dev_usecase.dart'
    as _i716;
import 'package:theadvance/src/domain/usecases/dev/get_saved_dev_usecase.dart'
    as _i715;
import 'package:theadvance/src/domain/usecases/dev/mini_app_dev_usecase.dart'
    as _i718;
import 'package:theadvance/src/domain/usecases/dev/remove_dev_usecase.dart'
    as _i719;
import 'package:theadvance/src/domain/usecases/dev/save_dev_usecase.dart'
    as _i717;
import 'package:theadvance/src/domain/usecases/eform/approving_eform_usecase.dart'
    as _i599;
import 'package:theadvance/src/domain/usecases/eform/approving_otp_eform_usecase.dart'
    as _i596;
import 'package:theadvance/src/domain/usecases/eform/approving_signal_eform_usecase.dart'
    as _i595;
import 'package:theadvance/src/domain/usecases/eform/create_eform_usecase.dart'
    as _i594;
import 'package:theadvance/src/domain/usecases/eform/get_detail_eform_usecase.dart'
    as _i600;
import 'package:theadvance/src/domain/usecases/eform/get_eform_request_type.dart'
    as _i597;
import 'package:theadvance/src/domain/usecases/eform/get_eform_usecase.dart'
    as _i601;
import 'package:theadvance/src/domain/usecases/eform/reject_eform_usecase.dart'
    as _i598;
import 'package:theadvance/src/domain/usecases/feedback/get_feedback_usecase.dart'
    as _i727;
import 'package:theadvance/src/domain/usecases/feedback/get_saved_feedback_usecase.dart'
    as _i728;
import 'package:theadvance/src/domain/usecases/feedback/remove_feedback_usecase.dart'
    as _i729;
import 'package:theadvance/src/domain/usecases/feedback/save_feedback_usecase.dart'
    as _i726;
import 'package:theadvance/src/domain/usecases/fonts/cache_text_scale_get_usecase.dart'
    as _i243;
import 'package:theadvance/src/domain/usecases/fonts/cache_text_scale_remove_usecase.dart'
    as _i233;
import 'package:theadvance/src/domain/usecases/fonts/cache_text_scale_save_usecase.dart'
    as _i234;
import 'package:theadvance/src/domain/usecases/food/order_food_create_report_usecase.dart'
    as _i525;
import 'package:theadvance/src/domain/usecases/food/order_food_created_usecase.dart'
    as _i524;
import 'package:theadvance/src/domain/usecases/food/order_food_delete_usecase.dart'
    as _i522;
import 'package:theadvance/src/domain/usecases/food/order_food_get_usecase.dart'
    as _i523;
import 'package:theadvance/src/domain/usecases/food/order_food_upload_usecase.dart'
    as _i320;
import 'package:theadvance/src/domain/usecases/food/set_default_address_food_usecase.dart'
    as _i526;
import 'package:theadvance/src/domain/usecases/general/cache_quick_action_get_usecase.dart'
    as _i772;
import 'package:theadvance/src/domain/usecases/general/cache_quick_action_remove_usecase.dart'
    as _i771;
import 'package:theadvance/src/domain/usecases/general/cache_quick_action_save_usecase.dart'
    as _i773;
import 'package:theadvance/src/domain/usecases/group_chat_detail/avatar_upload_group_chat_detail_usecase.dart'
    as _i273;
import 'package:theadvance/src/domain/usecases/group_chat_detail/change_owner_group_chat_detail_usecase.dart'
    as _i280;
import 'package:theadvance/src/domain/usecases/group_chat_detail/delete_group_usecase.dart'
    as _i276;
import 'package:theadvance/src/domain/usecases/group_chat_detail/file_load_group_chat_detail_usecase.dart'
    as _i284;
import 'package:theadvance/src/domain/usecases/group_chat_detail/get_group_chat_detail_usecase.dart'
    as _i279;
import 'package:theadvance/src/domain/usecases/group_chat_detail/get_rule_by_role_group_chat_detail_usecase.dart'
    as _i285;
import 'package:theadvance/src/domain/usecases/group_chat_detail/get_saved_group_chat_detail_usecase.dart'
    as _i287;
import 'package:theadvance/src/domain/usecases/group_chat_detail/get_user_exception_group_chat_detail_usecase.dart'
    as _i272;
import 'package:theadvance/src/domain/usecases/group_chat_detail/get_user_rules_group_chat_detail_usecase.dart'
    as _i283;
import 'package:theadvance/src/domain/usecases/group_chat_detail/link_load_group_chat_detail_usecase.dart'
    as _i281;
import 'package:theadvance/src/domain/usecases/group_chat_detail/media_load_group_chat_detail_usecase.dart'
    as _i277;
import 'package:theadvance/src/domain/usecases/group_chat_detail/member_info_load_group_chat_detail_usecase.dart'
    as _i275;
import 'package:theadvance/src/domain/usecases/group_chat_detail/remove_group_chat_detail_usecase.dart'
    as _i274;
import 'package:theadvance/src/domain/usecases/group_chat_detail/save_group_chat_detail_usecase.dart'
    as _i288;
import 'package:theadvance/src/domain/usecases/group_chat_detail/update_admin_rule_group_chat_detail_usecase.dart'
    as _i282;
import 'package:theadvance/src/domain/usecases/group_chat_detail/update_group_chat_detail_usecase.dart'
    as _i286;
import 'package:theadvance/src/domain/usecases/group_chat_detail/update_member_rule_group_chat_detail_usecase.dart'
    as _i278;
import 'package:theadvance/src/domain/usecases/helper/delete_latitude_usecase.dart'
    as _i238;
import 'package:theadvance/src/domain/usecases/helper/delete_longitude_usecase.dart'
    as _i237;
import 'package:theadvance/src/domain/usecases/helper/get_access_token_usecase.dart'
    as _i247;
import 'package:theadvance/src/domain/usecases/helper/get_app_version_usecase.dart'
    as _i245;
import 'package:theadvance/src/domain/usecases/helper/get_device_info_usecase.dart'
    as _i250;
import 'package:theadvance/src/domain/usecases/helper/get_firebase_token_usecase.dart'
    as _i248;
import 'package:theadvance/src/domain/usecases/helper/get_key_appsflyer_usecase.dart'
    as _i239;
import 'package:theadvance/src/domain/usecases/helper/get_latitude_usecase.dart'
    as _i246;
import 'package:theadvance/src/domain/usecases/helper/get_longitude_usecase.dart'
    as _i249;
import 'package:theadvance/src/domain/usecases/helper/get_phone_usecase.dart'
    as _i244;
import 'package:theadvance/src/domain/usecases/helper/save_app_version_usecase.dart'
    as _i240;
import 'package:theadvance/src/domain/usecases/helper/save_device_info_usecase.dart'
    as _i235;
import 'package:theadvance/src/domain/usecases/helper/save_key_appflyer_usecase.dart'
    as _i242;
import 'package:theadvance/src/domain/usecases/helper/save_latitude_usecase.dart'
    as _i236;
import 'package:theadvance/src/domain/usecases/helper/save_longitude_usecase.dart'
    as _i241;
import 'package:theadvance/src/domain/usecases/history_checkin/fetch_history_checkin_usecase.dart'
    as _i482;
import 'package:theadvance/src/domain/usecases/home/<USER>'
    as _i814;
import 'package:theadvance/src/domain/usecases/home/<USER>'
    as _i551;
import 'package:theadvance/src/domain/usecases/home/<USER>'
    as _i552;
import 'package:theadvance/src/domain/usecases/home/<USER>'
    as _i815;
import 'package:theadvance/src/domain/usecases/hr_organization/get_hr_organization_usecase.dart'
    as _i613;
import 'package:theadvance/src/domain/usecases/hr_organization/get_saved_hr_organization_usecase.dart'
    as _i614;
import 'package:theadvance/src/domain/usecases/hr_organization/remove_hr_organization_usecase.dart'
    as _i615;
import 'package:theadvance/src/domain/usecases/hr_organization/save_hr_organization_usecase.dart'
    as _i612;
import 'package:theadvance/src/domain/usecases/important_notes/get_important_notes_usecase.dart'
    as _i495;
import 'package:theadvance/src/domain/usecases/important_notes/get_note_category_important_notes_usecase.dart'
    as _i494;
import 'package:theadvance/src/domain/usecases/important_notes/get_saved_important_notes_usecase.dart'
    as _i492;
import 'package:theadvance/src/domain/usecases/important_notes/remove_important_notes_usecase.dart'
    as _i496;
import 'package:theadvance/src/domain/usecases/important_notes/save_important_notes_usecase.dart'
    as _i493;
import 'package:theadvance/src/domain/usecases/kpi_employee/get_kpi_employee_detail_usecase.dart'
    as _i690;
import 'package:theadvance/src/domain/usecases/kpi_employee/get_kpi_employee_usecase.dart'
    as _i691;
import 'package:theadvance/src/domain/usecases/like_list/get_like_list_usecase.dart'
    as _i388;
import 'package:theadvance/src/domain/usecases/like_list/post_like_comment_usecase.dart'
    as _i387;
import 'package:theadvance/src/domain/usecases/like_list/post_like_story_usecase.dart'
    as _i386;
import 'package:theadvance/src/domain/usecases/list_customer/get_list_customer_usecase.dart'
    as _i853;
import 'package:theadvance/src/domain/usecases/list_customer/get_saved_list_customer_usecase.dart'
    as _i852;
import 'package:theadvance/src/domain/usecases/list_customer/get_status_list_customer_usecase.dart'
    as _i851;
import 'package:theadvance/src/domain/usecases/list_customer/remove_list_customer_usecase.dart'
    as _i855;
import 'package:theadvance/src/domain/usecases/list_customer/save_list_customer_usecase.dart'
    as _i854;
import 'package:theadvance/src/domain/usecases/list_customer/search_list_customer_usecase.dart'
    as _i856;
import 'package:theadvance/src/domain/usecases/location_google/get_address_current_usecase.dart'
    as _i610;
import 'package:theadvance/src/domain/usecases/location_google/get_address_near_usecase.dart'
    as _i608;
import 'package:theadvance/src/domain/usecases/location_google/get_address_search_usecase.dart'
    as _i611;
import 'package:theadvance/src/domain/usecases/location_google/get_location_google_usecase.dart'
    as _i609;
import 'package:theadvance/src/domain/usecases/login/socket_access_token_get_login_usecase.dart'
    as _i587;
import 'package:theadvance/src/domain/usecases/media/upload_avatar_usecase.dart'
    as _i652;
import 'package:theadvance/src/domain/usecases/media/upload_background_usecase.dart'
    as _i653;
import 'package:theadvance/src/domain/usecases/media/upload_checkin_image_usecase.dart'
    as _i651;
import 'package:theadvance/src/domain/usecases/media/upload_feedback_usecase.dart'
    as _i650;
import 'package:theadvance/src/domain/usecases/media/upload_kyc_usecase.dart'
    as _i654;
import 'package:theadvance/src/domain/usecases/medical_department_list/get_medical_department_list_usecase.dart'
    as _i373;
import 'package:theadvance/src/domain/usecases/medical_department_list/get_saved_medical_department_list_usecase.dart'
    as _i372;
import 'package:theadvance/src/domain/usecases/medical_department_list/remove_medical_department_list_usecase.dart'
    as _i375;
import 'package:theadvance/src/domain/usecases/medical_department_list/save_medical_department_list_usecase.dart'
    as _i374;
import 'package:theadvance/src/domain/usecases/medical_log_detail/create_log_medical_detail_usecase.dart'
    as _i873;
import 'package:theadvance/src/domain/usecases/medical_log_detail/doctor_list_get_medical_log_detail_usecase.dart'
    as _i863;
import 'package:theadvance/src/domain/usecases/medical_log_detail/dosage_list_get_medical_log_detail_usecase.dart'
    as _i865;
import 'package:theadvance/src/domain/usecases/medical_log_detail/get_medical_log_detail_usecase.dart'
    as _i875;
import 'package:theadvance/src/domain/usecases/medical_log_detail/get_origin_status_medical_log_detail_usecase.dart'
    as _i871;
import 'package:theadvance/src/domain/usecases/medical_log_detail/get_post_sai_medical_log_detail_usecase.dart'
    as _i876;
import 'package:theadvance/src/domain/usecases/medical_log_detail/get_saved_medical_log_detail_usecase.dart'
    as _i872;
import 'package:theadvance/src/domain/usecases/medical_log_detail/get_skin_machine_medical_log_detail_usecase.dart'
    as _i874;
import 'package:theadvance/src/domain/usecases/medical_log_detail/get_tattoo_color_medical_log_detail_usecase.dart'
    as _i864;
import 'package:theadvance/src/domain/usecases/medical_log_detail/get_tattoo_time_medical_log_detail_usecase.dart'
    as _i869;
import 'package:theadvance/src/domain/usecases/medical_log_detail/ha_point_list_get_medical_log_detail_usecase.dart'
    as _i862;
import 'package:theadvance/src/domain/usecases/medical_log_detail/khacnho_list_get_medical_log_detail_usecase.dart'
    as _i870;
import 'package:theadvance/src/domain/usecases/medical_log_detail/medicine_list_get_medical_log_detail_usecase.dart'
    as _i866;
import 'package:theadvance/src/domain/usecases/medical_log_detail/remove_medical_log_detail_usecase.dart'
    as _i868;
import 'package:theadvance/src/domain/usecases/medical_log_detail/save_medical_log_detail_usecase.dart'
    as _i877;
import 'package:theadvance/src/domain/usecases/medical_log_detail/update_log_medical_detail_usecase.dart'
    as _i867;
import 'package:theadvance/src/domain/usecases/medical_product_create/get_saved_medical_product_create_usecase.dart'
    as _i509;
import 'package:theadvance/src/domain/usecases/medical_product_create/medical_product_create_usecase.dart'
    as _i512;
import 'package:theadvance/src/domain/usecases/medical_product_create/products_medical_product_create_usecase.dart'
    as _i508;
import 'package:theadvance/src/domain/usecases/medical_product_create/remove_medical_product_create_usecase.dart'
    as _i510;
import 'package:theadvance/src/domain/usecases/medical_product_create/save_medical_product_create_usecase.dart'
    as _i511;
import 'package:theadvance/src/domain/usecases/medical_service_create/get_saved_medical_service_create_usecase.dart'
    as _i723;
import 'package:theadvance/src/domain/usecases/medical_service_create/medical_service_create_usecase.dart'
    as _i725;
import 'package:theadvance/src/domain/usecases/medical_service_create/methods_medical_service_create_usecase.dart'
    as _i722;
import 'package:theadvance/src/domain/usecases/medical_service_create/remove_medical_service_create_usecase.dart'
    as _i720;
import 'package:theadvance/src/domain/usecases/medical_service_create/save_medical_service_create_usecase.dart'
    as _i724;
import 'package:theadvance/src/domain/usecases/medical_service_create/services_medical_service_create_usecase.dart'
    as _i721;
import 'package:theadvance/src/domain/usecases/medical_service_list/get_medical_service_list_usecase.dart'
    as _i471;
import 'package:theadvance/src/domain/usecases/medical_service_list/get_saved_medical_service_list_usecase.dart'
    as _i468;
import 'package:theadvance/src/domain/usecases/medical_service_list/remove_medical_service_list_usecase.dart'
    as _i470;
import 'package:theadvance/src/domain/usecases/medical_service_list/save_medical_service_list_usecase.dart'
    as _i469;
import 'package:theadvance/src/domain/usecases/medical_service_log_list/get_medical_service_log_list_usecase.dart'
    as _i810;
import 'package:theadvance/src/domain/usecases/medical_service_log_list/get_saved_medical_service_log_list_usecase.dart'
    as _i811;
import 'package:theadvance/src/domain/usecases/medical_service_log_list/remove_medical_service_log_list_usecase.dart'
    as _i809;
import 'package:theadvance/src/domain/usecases/medical_service_log_list/save_medical_service_log_list_usecase.dart'
    as _i808;
import 'package:theadvance/src/domain/usecases/medical_template_list/get_medical_template_list_usecase.dart'
    as _i859;
import 'package:theadvance/src/domain/usecases/medical_template_list/get_saved_medical_template_list_usecase.dart'
    as _i860;
import 'package:theadvance/src/domain/usecases/medical_template_list/medical_template_detail_get_medical_template_list_usecase.dart'
    as _i858;
import 'package:theadvance/src/domain/usecases/medical_template_list/remove_medical_template_list_usecase.dart'
    as _i857;
import 'package:theadvance/src/domain/usecases/medical_template_list/save_medical_template_list_usecase.dart'
    as _i861;
import 'package:theadvance/src/domain/usecases/medicine_detail/create_medicine_detail_usecase.dart'
    as _i706;
import 'package:theadvance/src/domain/usecases/medicine_detail/get_medicine_detail_usecase.dart'
    as _i709;
import 'package:theadvance/src/domain/usecases/medicine_detail/get_saved_medicine_detail_usecase.dart'
    as _i708;
import 'package:theadvance/src/domain/usecases/medicine_detail/get_unit_medicine_detail_usecase.dart'
    as _i705;
import 'package:theadvance/src/domain/usecases/medicine_detail/remove_medicine_detail_usecase.dart'
    as _i704;
import 'package:theadvance/src/domain/usecases/medicine_detail/save_medicine_detail_usecase.dart'
    as _i710;
import 'package:theadvance/src/domain/usecases/medicine_detail/update_medicine_detail_usecase.dart'
    as _i707;
import 'package:theadvance/src/domain/usecases/news/get_detail_news_usecase.dart'
    as _i590;
import 'package:theadvance/src/domain/usecases/news/get_news_usecase.dart'
    as _i588;
import 'package:theadvance/src/domain/usecases/news/search_news_usecase.dart'
    as _i589;
import 'package:theadvance/src/domain/usecases/note_details/create_note_details_usecase.dart'
    as _i732;
import 'package:theadvance/src/domain/usecases/note_details/get_note_details_usecase.dart'
    as _i736;
import 'package:theadvance/src/domain/usecases/note_details/get_saved_note_details_usecase.dart'
    as _i735;
import 'package:theadvance/src/domain/usecases/note_details/remove_note_details_usecase.dart'
    as _i734;
import 'package:theadvance/src/domain/usecases/note_details/save_note_details_usecase.dart'
    as _i737;
import 'package:theadvance/src/domain/usecases/note_details/update_note_details_usecase.dart'
    as _i733;
import 'package:theadvance/src/domain/usecases/notification/get_detail_notification_usecase.dart'
    as _i633;
import 'package:theadvance/src/domain/usecases/notification/get_navigation_info_usecase.dart'
    as _i634;
import 'package:theadvance/src/domain/usecases/notification/get_notifications_usecase.dart'
    as _i636;
import 'package:theadvance/src/domain/usecases/notification/post_read_notifcation_usecase.dart'
    as _i632;
import 'package:theadvance/src/domain/usecases/notification/read_all_notification_usecase.dart'
    as _i637;
import 'package:theadvance/src/domain/usecases/notification/search_notifications_usecase.dart'
    as _i635;
import 'package:theadvance/src/domain/usecases/notification_list/delete_notification_usecase.dart'
    as _i262;
import 'package:theadvance/src/domain/usecases/notification_list/get_notification_list_usecase.dart'
    as _i260;
import 'package:theadvance/src/domain/usecases/notification_list/get_saved_notification_list_usecase.dart'
    as _i264;
import 'package:theadvance/src/domain/usecases/notification_list/put_read_all_social_usecase.dart'
    as _i261;
import 'package:theadvance/src/domain/usecases/notification_list/remove_notification_list_usecase.dart'
    as _i259;
import 'package:theadvance/src/domain/usecases/notification_list/save_notification_list_usecase.dart'
    as _i263;
import 'package:theadvance/src/domain/usecases/product_confirm/approval_product_detail_confirm_usecase.dart'
    as _i584;
import 'package:theadvance/src/domain/usecases/product_confirm/get_product_confirm_branch_usecase.dart'
    as _i585;
import 'package:theadvance/src/domain/usecases/product_confirm/get_product_confirm_usecase.dart'
    as _i582;
import 'package:theadvance/src/domain/usecases/product_confirm/get_product_detail_confirm_usecase.dart'
    as _i583;
import 'package:theadvance/src/domain/usecases/product_confirm/reject_product_detail_confirm_usecase.dart'
    as _i586;
import 'package:theadvance/src/domain/usecases/province/get_province_usecase.dart'
    as _i628;
import 'package:theadvance/src/domain/usecases/px_list/get_px_list_usecase.dart'
    as _i805;
import 'package:theadvance/src/domain/usecases/px_list/get_saved_px_list_usecase.dart'
    as _i804;
import 'package:theadvance/src/domain/usecases/px_list/remove_px_list_usecase.dart'
    as _i807;
import 'package:theadvance/src/domain/usecases/px_list/save_px_list_usecase.dart'
    as _i806;
import 'package:theadvance/src/domain/usecases/px_recheck/assign_px_recheck_update_usecase.dart'
    as _i847;
import 'package:theadvance/src/domain/usecases/px_recheck/assigns_fetch_px_recheck_usecase.dart'
    as _i881;
import 'package:theadvance/src/domain/usecases/px_recheck/note_finish_px_recheck_usecase.dart'
    as _i879;
import 'package:theadvance/src/domain/usecases/px_recheck/work_status_update_px_recheck_usecase.dart'
    as _i880;
import 'package:theadvance/src/domain/usecases/px_task_list/get_px_task_list_usecase.dart'
    as _i520;
import 'package:theadvance/src/domain/usecases/px_task_list/get_saved_px_task_list_usecase.dart'
    as _i518;
import 'package:theadvance/src/domain/usecases/px_task_list/remove_px_task_list_usecase.dart'
    as _i517;
import 'package:theadvance/src/domain/usecases/px_task_list/save_px_task_list_usecase.dart'
    as _i519;
import 'package:theadvance/src/domain/usecases/px_unasigned/get_px_customer_list_usecase.dart'
    as _i744;
import 'package:theadvance/src/domain/usecases/px_unasigned/get_saved_px_unasigned_usecase.dart'
    as _i746;
import 'package:theadvance/src/domain/usecases/px_unasigned/remove_px_unasigned_usecase.dart'
    as _i743;
import 'package:theadvance/src/domain/usecases/px_unasigned/save_px_unasigned_usecase.dart'
    as _i745;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/assign_px_unasigned_update_usecase.dart'
    as _i916;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/employees_fetch_px_unasigned_update_usecase.dart'
    as _i915;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/employees_in_room_usecase.dart'
    as _i914;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/get_px_unasigned_update_usecase.dart'
    as _i918;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/get_saved_px_unasigned_update_usecase.dart'
    as _i921;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/remove_px_unasigned_update_usecase.dart'
    as _i920;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/save_px_unasigned_update_usecase.dart'
    as _i919;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/works_fetch_px_unasigned_update_usecase.dart'
    as _i917;
import 'package:theadvance/src/domain/usecases/rating_human/get_question_detail_usecase.dart'
    as _i700;
import 'package:theadvance/src/domain/usecases/rating_human/get_rating_human_usecase.dart'
    as _i702;
import 'package:theadvance/src/domain/usecases/rating_human/save_question_usecase.dart'
    as _i701;
import 'package:theadvance/src/domain/usecases/rating_human/submit_question_usecase.dart'
    as _i699;
import 'package:theadvance/src/domain/usecases/request/get_list_support_request_usecase.dart'
    as _i927;
import 'package:theadvance/src/domain/usecases/request/send_support_request_usecase.dart'
    as _i928;
import 'package:theadvance/src/domain/usecases/schedule_details/get_saved_schedule_details_usecase.dart'
    as _i568;
import 'package:theadvance/src/domain/usecases/schedule_details/get_schedule_details_usecase.dart'
    as _i569;
import 'package:theadvance/src/domain/usecases/schedule_details/remove_schedule_details_usecase.dart'
    as _i566;
import 'package:theadvance/src/domain/usecases/schedule_details/save_schedule_details_usecase.dart'
    as _i567;
import 'package:theadvance/src/domain/usecases/select_px_room/get_saved_select_px_room_usecase.dart'
    as _i421;
import 'package:theadvance/src/domain/usecases/select_px_room/get_select_px_room_usecase.dart'
    as _i418;
import 'package:theadvance/src/domain/usecases/select_px_room/remove_select_px_room_usecase.dart'
    as _i419;
import 'package:theadvance/src/domain/usecases/select_px_room/room_change_select_px_room_usecase.dart'
    as _i420;
import 'package:theadvance/src/domain/usecases/select_px_room/save_select_px_room_usecase.dart'
    as _i422;
import 'package:theadvance/src/domain/usecases/service_and_product/get_category_service_and_product_usecase.dart'
    as _i895;
import 'package:theadvance/src/domain/usecases/service_and_product/get_saved_service_and_product_usecase.dart'
    as _i894;
import 'package:theadvance/src/domain/usecases/service_and_product/get_service_and_product_actions_usecase.dart'
    as _i888;
import 'package:theadvance/src/domain/usecases/service_and_product/get_service_and_product_usecase.dart'
    as _i889;
import 'package:theadvance/src/domain/usecases/service_and_product/products_get_service_and_product_usecase.dart'
    as _i892;
import 'package:theadvance/src/domain/usecases/service_and_product/remove_service_and_product_usecase.dart'
    as _i893;
import 'package:theadvance/src/domain/usecases/service_and_product/save_service_and_product_usecase.dart'
    as _i890;
import 'package:theadvance/src/domain/usecases/service_and_product/services_get_service_and_product_usecase.dart'
    as _i891;
import 'package:theadvance/src/domain/usecases/service_detail/doctor_fetch_service_detail_usecase.dart'
    as _i655;
import 'package:theadvance/src/domain/usecases/service_detail/employee_fetch_service_detail_usecase.dart'
    as _i656;
import 'package:theadvance/src/domain/usecases/setting/get_font_option_usecase.dart'
    as _i411;
import 'package:theadvance/src/domain/usecases/setting/get_language_option_usecase.dart'
    as _i406;
import 'package:theadvance/src/domain/usecases/setting/get_theme_option_usecase.dart'
    as _i407;
import 'package:theadvance/src/domain/usecases/setting/is_dark_mode_usecase.dart'
    as _i410;
import 'package:theadvance/src/domain/usecases/setting/is_light_mode_usecase.dart'
    as _i412;
import 'package:theadvance/src/domain/usecases/setting/save_font_option_usecase.dart'
    as _i405;
import 'package:theadvance/src/domain/usecases/setting/save_language_option_usecase.dart'
    as _i408;
import 'package:theadvance/src/domain/usecases/setting/save_theme_option_usecase.dart'
    as _i409;
import 'package:theadvance/src/domain/usecases/staff/get_department_usecase.dart'
    as _i591;
import 'package:theadvance/src/domain/usecases/staff/get_function_room_usecase.dart'
    as _i592;
import 'package:theadvance/src/domain/usecases/staff/get_staff_usecase.dart'
    as _i593;
import 'package:theadvance/src/domain/usecases/staff_evaluation_periods/get_saved_staff_evaluation_periods_usecase.dart'
    as _i501;
import 'package:theadvance/src/domain/usecases/staff_evaluation_periods/get_staff_evaluation_periods_usecase.dart'
    as _i502;
import 'package:theadvance/src/domain/usecases/staff_evaluation_periods/remove_staff_evaluation_periods_usecase.dart'
    as _i499;
import 'package:theadvance/src/domain/usecases/staff_evaluation_periods/save_staff_evaluation_periods_usecase.dart'
    as _i500;
import 'package:theadvance/src/domain/usecases/sticker_social/create_sticker_usecase.dart'
    as _i339;
import 'package:theadvance/src/domain/usecases/sticker_social/get_sticker_only_set_usecase.dart'
    as _i334;
import 'package:theadvance/src/domain/usecases/sticker_social/get_sticker_recent_usecase.dart'
    as _i335;
import 'package:theadvance/src/domain/usecases/sticker_social/get_sticker_set_usecase.dart'
    as _i338;
import 'package:theadvance/src/domain/usecases/sticker_social/get_sticker_usecase.dart'
    as _i341;
import 'package:theadvance/src/domain/usecases/sticker_social/remove_sticker_set_usecase.dart'
    as _i342;
import 'package:theadvance/src/domain/usecases/sticker_social/remove_sticker_usecase.dart'
    as _i336;
import 'package:theadvance/src/domain/usecases/sticker_social/update_sticker_recent_usecase.dart'
    as _i337;
import 'package:theadvance/src/domain/usecases/sticker_social/update_sticker_set_usecase.dart'
    as _i333;
import 'package:theadvance/src/domain/usecases/sticker_social/upload_sticker_usecase.dart'
    as _i340;
import 'package:theadvance/src/domain/usecases/story_detail/get_saved_story_detail_usecase.dart'
    as _i173;
import 'package:theadvance/src/domain/usecases/story_detail/get_story_detail_usecase.dart'
    as _i174;
import 'package:theadvance/src/domain/usecases/story_detail/remove_story_detail_usecase.dart'
    as _i172;
import 'package:theadvance/src/domain/usecases/story_detail/save_story_detail_usecase.dart'
    as _i175;
import 'package:theadvance/src/domain/usecases/story_list/delete_story_usecase.dart'
    as _i464;
import 'package:theadvance/src/domain/usecases/story_list/delete_story_vote_usecase.dart'
    as _i463;
import 'package:theadvance/src/domain/usecases/story_list/get_emoji_list_usecase.dart'
    as _i454;
import 'package:theadvance/src/domain/usecases/story_list/get_story_list_usecase.dart'
    as _i455;
import 'package:theadvance/src/domain/usecases/story_list/get_story_rule_usecase.dart'
    as _i465;
import 'package:theadvance/src/domain/usecases/story_list/get_story_search_usecase.dart'
    as _i456;
import 'package:theadvance/src/domain/usecases/story_list/get_total_notification_usecase.dart'
    as _i459;
import 'package:theadvance/src/domain/usecases/story_list/get_vote_users_usecase.dart'
    as _i462;
import 'package:theadvance/src/domain/usecases/story_list/post_story_usecase.dart'
    as _i457;
import 'package:theadvance/src/domain/usecases/story_list/put_story_vote_usecase.dart'
    as _i458;
import 'package:theadvance/src/domain/usecases/story_list/social_upload_file_usecase.dart'
    as _i461;
import 'package:theadvance/src/domain/usecases/story_list/update_story_usecase.dart'
    as _i460;
import 'package:theadvance/src/domain/usecases/story_person_list/get_story_person_list_usecase.dart'
    as _i403;
import 'package:theadvance/src/domain/usecases/story_person_list/get_story_person_list_user_usecase.dart'
    as _i404;
import 'package:theadvance/src/domain/usecases/tag_image/get_combo_tag_usecase.dart'
    as _i823;
import 'package:theadvance/src/domain/usecases/tag_image/get_image_by_combo_tag_usecase.dart'
    as _i824;
import 'package:theadvance/src/domain/usecases/tag_list/get_tag_list_usecase.dart'
    as _i269;
import 'package:theadvance/src/domain/usecases/taking_care_customer/bot_type_load_taking_care_customer_usecase.dart'
    as _i801;
import 'package:theadvance/src/domain/usecases/taking_care_customer/check_employee_in_room_taking_care_customer_usecase.dart'
    as _i797;
import 'package:theadvance/src/domain/usecases/taking_care_customer/create_support_taking_care_customer_usecase.dart'
    as _i792;
import 'package:theadvance/src/domain/usecases/taking_care_customer/create_treatment_details_taking_care_customer_usecase.dart'
    as _i796;
import 'package:theadvance/src/domain/usecases/taking_care_customer/finish_task_taking_care_customer_usecase.dart'
    as _i790;
import 'package:theadvance/src/domain/usecases/taking_care_customer/get_saved_taking_care_customer_usecase.dart'
    as _i791;
import 'package:theadvance/src/domain/usecases/taking_care_customer/get_section_taking_care_customer_usecase.dart'
    as _i793;
import 'package:theadvance/src/domain/usecases/taking_care_customer/get_taking_care_customer_usecase.dart'
    as _i799;
import 'package:theadvance/src/domain/usecases/taking_care_customer/get_treatment_photo_taking_care_customer_usecase.dart'
    as _i800;
import 'package:theadvance/src/domain/usecases/taking_care_customer/noti_bot_type_load_taking_care_customer_usecase.dart'
    as _i788;
import 'package:theadvance/src/domain/usecases/taking_care_customer/remove_image_taking_care_customer_usecase.dart'
    as _i789;
import 'package:theadvance/src/domain/usecases/taking_care_customer/remove_taking_care_customer_usecase.dart'
    as _i794;
import 'package:theadvance/src/domain/usecases/taking_care_customer/save_taking_care_customer_usecase.dart'
    as _i795;
import 'package:theadvance/src/domain/usecases/taking_care_customer/update_service_detail_taking_care_customer_usecase.dart'
    as _i798;
import 'package:theadvance/src/domain/usecases/taking_care_customer/upload_images_taking_care_customer_usecase.dart'
    as _i803;
import 'package:theadvance/src/domain/usecases/taking_care_customer/upload_record_taking_care_customer_usecase.dart'
    as _i802;
import 'package:theadvance/src/domain/usecases/task/creating_task_usecase.dart'
    as _i395;
import 'package:theadvance/src/domain/usecases/task/get_detail_job_scheduler_usecase.dart'
    as _i400;
import 'package:theadvance/src/domain/usecases/task/get_general_job_scheduler_usecase.dart'
    as _i399;
import 'package:theadvance/src/domain/usecases/task/get_job_scheduler_usecase.dart'
    as _i397;
import 'package:theadvance/src/domain/usecases/task/get_repeat_task_usecase.dart'
    as _i396;
import 'package:theadvance/src/domain/usecases/task/submit_job_scheduler_usecase.dart'
    as _i398;
import 'package:theadvance/src/domain/usecases/ticket_active/complete_ticket_usecase.dart'
    as _i604;
import 'package:theadvance/src/domain/usecases/ticket_active/create_ticket_active_usecase.dart'
    as _i606;
import 'package:theadvance/src/domain/usecases/ticket_active/get_ticket_active_usecase.dart'
    as _i605;
import 'package:theadvance/src/domain/usecases/ticket_detail/confirm_ticket_usecase.dart'
    as _i560;
import 'package:theadvance/src/domain/usecases/ticket_detail/get_saved_ticket_detail_usecase.dart'
    as _i557;
import 'package:theadvance/src/domain/usecases/ticket_detail/get_ticket_detail_usecase.dart'
    as _i555;
import 'package:theadvance/src/domain/usecases/ticket_detail/recept_ticket_usecase.dart'
    as _i558;
import 'package:theadvance/src/domain/usecases/ticket_detail/remove_ticket_detail_usecase.dart'
    as _i561;
import 'package:theadvance/src/domain/usecases/ticket_detail/save_ticket_detail_usecase.dart'
    as _i554;
import 'package:theadvance/src/domain/usecases/ticket_detail/ticket_detail_reason_usecase.dart'
    as _i556;
import 'package:theadvance/src/domain/usecases/ticket_detail/ticket_rework_usecase.dart'
    as _i559;
import 'package:theadvance/src/domain/usecases/ticketv2/create_ticket_usecase.dart'
    as _i431;
import 'package:theadvance/src/domain/usecases/ticketv2/get_my_ticket_usecase.dart'
    as _i434;
import 'package:theadvance/src/domain/usecases/ticketv2/get_ticket_all_group_usecase.dart'
    as _i430;
import 'package:theadvance/src/domain/usecases/ticketv2/get_ticket_all_type_usecase.dart'
    as _i436;
import 'package:theadvance/src/domain/usecases/ticketv2/get_ticket_created_type_usecase.dart'
    as _i429;
import 'package:theadvance/src/domain/usecases/ticketv2/get_ticket_group_type_usecase.dart'
    as _i437;
import 'package:theadvance/src/domain/usecases/ticketv2/get_ticket_ticketv2_usecase.dart'
    as _i432;
import 'package:theadvance/src/domain/usecases/ticketv2/ticket_upload_file_usecase.dart'
    as _i433;
import 'package:theadvance/src/domain/usecases/ticketv2/update_ticket_usecase.dart'
    as _i435;
import 'package:theadvance/src/domain/usecases/tracking/send_event_use_case.dart'
    as _i72;
import 'package:theadvance/src/domain/usecases/tracking/track_banner_use_case.dart'
    as _i70;
import 'package:theadvance/src/domain/usecases/tracking/track_login_use_case.dart'
    as _i66;
import 'package:theadvance/src/domain/usecases/tracking/track_notification_use_case.dart'
    as _i75;
import 'package:theadvance/src/domain/usecases/tracking/track_open_app_use_case.dart'
    as _i71;
import 'package:theadvance/src/domain/usecases/tracking/track_open_landing_page_use_case.dart'
    as _i69;
import 'package:theadvance/src/domain/usecases/tracking/track_popup_use_case.dart'
    as _i73;
import 'package:theadvance/src/domain/usecases/tracking/track_register_use_case.dart'
    as _i74;
import 'package:theadvance/src/domain/usecases/tracking/track_valid_install_use_case.dart'
    as _i68;
import 'package:theadvance/src/domain/usecases/universal_qr_scan/universal_qr_scan_usecase.dart'
    as _i367;
import 'package:theadvance/src/domain/usecases/user/cache_login_stringee_get_usecase.dart'
    as _i777;
import 'package:theadvance/src/domain/usecases/user/cache_login_stringee_remove_usecase.dart'
    as _i774;
import 'package:theadvance/src/domain/usecases/user/cache_login_stringee_save_usecase.dart'
    as _i779;
import 'package:theadvance/src/domain/usecases/user/cache_user_usecase.dart'
    as _i778;
import 'package:theadvance/src/domain/usecases/user/change_password_usecase.dart'
    as _i768;
import 'package:theadvance/src/domain/usecases/user/check_employee_usecase.dart'
    as _i751;
import 'package:theadvance/src/domain/usecases/user/check_permission_user_usecase.dart'
    as _i660;
import 'package:theadvance/src/domain/usecases/user/check_phone_usecase.dart'
    as _i753;
import 'package:theadvance/src/domain/usecases/user/checkin_permission_check_user_usecase.dart'
    as _i658;
import 'package:theadvance/src/domain/usecases/user/checkin_usecase.dart'
    as _i362;
import 'package:theadvance/src/domain/usecases/user/clear_cache_usecase.dart'
    as _i358;
import 'package:theadvance/src/domain/usecases/user/confirm_otp_usecase.dart'
    as _i763;
import 'package:theadvance/src/domain/usecases/user/delete_home_menu_cache_usecase.dart'
    as _i361;
import 'package:theadvance/src/domain/usecases/user/delete_profile_usecase.dart'
    as _i359;
import 'package:theadvance/src/domain/usecases/user/delete_token_usecase.dart'
    as _i357;
import 'package:theadvance/src/domain/usecases/user/get_configuration_usecase.dart'
    as _i762;
import 'package:theadvance/src/domain/usecases/user/get_enable_online_logger_usecase.dart'
    as _i758;
import 'package:theadvance/src/domain/usecases/user/get_otp_usecase.dart'
    as _i752;
import 'package:theadvance/src/domain/usecases/user/get_profile_usecase.dart'
    as _i759;
import 'package:theadvance/src/domain/usecases/user/get_profiles_usecase.dart'
    as _i757;
import 'package:theadvance/src/domain/usecases/user/get_user_info_usecase.dart'
    as _i770;
import 'package:theadvance/src/domain/usecases/user/has_token_usecase.dart'
    as _i776;
import 'package:theadvance/src/domain/usecases/user/has_user_data_usecase.dart'
    as _i355;
import 'package:theadvance/src/domain/usecases/user/is_show_boarding.dart'
    as _i775;
import 'package:theadvance/src/domain/usecases/user/login_social_usecase.dart'
    as _i765;
import 'package:theadvance/src/domain/usecases/user/login_usecase.dart'
    as _i754;
import 'package:theadvance/src/domain/usecases/user/logout_usecase.dart'
    as _i755;
import 'package:theadvance/src/domain/usecases/user/persist_token_usecase.dart'
    as _i356;
import 'package:theadvance/src/domain/usecases/user/reset_password_usecase.dart'
    as _i760;
import 'package:theadvance/src/domain/usecases/user/save_account_usecase.dart'
    as _i360;
import 'package:theadvance/src/domain/usecases/user/save_enable_online_logger_usecase.dart'
    as _i353;
import 'package:theadvance/src/domain/usecases/user/save_navigation_usecase.dart'
    as _i352;
import 'package:theadvance/src/domain/usecases/user/save_profile_usecase.dart'
    as _i351;
import 'package:theadvance/src/domain/usecases/user/send_kyc_photos_setting_usecase.dart'
    as _i657;
import 'package:theadvance/src/domain/usecases/user/show_onboarding_usecase.dart'
    as _i354;
import 'package:theadvance/src/domain/usecases/user/stringee_token_fetch_user_usecase.dart'
    as _i659;
import 'package:theadvance/src/domain/usecases/user/submit_feedback_usecase.dart'
    as _i766;
import 'package:theadvance/src/domain/usecases/user/update_bio_usecase.dart'
    as _i764;
import 'package:theadvance/src/domain/usecases/user/update_profile_usecase.dart'
    as _i750;
import 'package:theadvance/src/domain/usecases/user/upload_audios_usecase.dart'
    as _i767;
import 'package:theadvance/src/domain/usecases/user/upload_files_usecase.dart'
    as _i761;
import 'package:theadvance/src/domain/usecases/user/upload_images_usecase.dart'
    as _i756;
import 'package:theadvance/src/domain/usecases/user/upload_usecase.dart'
    as _i769;
import 'package:theadvance/src/domain/usecases/user/user_deletion_usecase.dart'
    as _i661;
import 'package:theadvance/src/domain/usecases/user_list/get_saved_user_list_usecase.dart'
    as _i826;
import 'package:theadvance/src/domain/usecases/user_list/get_user_list_usecase.dart'
    as _i827;
import 'package:theadvance/src/domain/usecases/user_list/remove_user_list_usecase.dart'
    as _i828;
import 'package:theadvance/src/domain/usecases/user_list/save_user_list_usecase.dart'
    as _i829;
import 'package:theadvance/src/domain/usecases/user_ticket/get_dropdown_list_status.dart'
    as _i442;
import 'package:theadvance/src/domain/usecases/user_ticket/get_user_ticket.dart'
    as _i443;
import 'package:theadvance/src/module/register_module.dart' as _i984;
import 'package:theadvance/src/presentation/_blocs/authentication/authentication_bloc.dart'
    as _i939;
import 'package:theadvance/src/presentation/_blocs/collaborator_user/collaborator_user_bloc.dart'
    as _i786;
import 'package:theadvance/src/presentation/_blocs/event_authentication_bloc/event_authentication_bloc.dart'
    as _i3;
import 'package:theadvance/src/presentation/_blocs/general_bloc/general_bloc.dart'
    as _i962;
import 'package:theadvance/src/presentation/action_attendance/bloc/action_attendance_bloc.dart'
    as _i969;
import 'package:theadvance/src/presentation/assign_task/bloc/assign_task_bloc.dart'
    as _i981;
import 'package:theadvance/src/presentation/bed_selection/bloc/bed_selection_bloc.dart'
    as _i845;
import 'package:theadvance/src/presentation/branch_chat_list/bloc/branch_chat_list_bloc.dart'
    as _i887;
import 'package:theadvance/src/presentation/branch_selection/bloc/branch_selection_bloc.dart'
    as _i835;
import 'package:theadvance/src/presentation/chat/bloc/chat_bloc.dart' as _i425;
import 'package:theadvance/src/presentation/chat_list/bloc/chat_list_bloc.dart'
    as _i749;
import 'package:theadvance/src/presentation/chat_select_branch/bloc/chat_select_branch_bloc.dart'
    as _i521;
import 'package:theadvance/src/presentation/checkin_photo/bloc/checkin_photo_bloc.dart'
    as _i937;
import 'package:theadvance/src/presentation/collaborator/checkin_reminder/bloc/checkin_reminder_bloc.dart'
    as _i813;
import 'package:theadvance/src/presentation/collaborator/confirm_otp/bloc/confirm_otp_bloc.dart'
    as _i850;
import 'package:theadvance/src/presentation/collaborator/create_support_request/bloc/create_support_requests_bloc.dart'
    as _i971;
import 'package:theadvance/src/presentation/collaborator/creating_eform/bloc/creating_eform_bloc.dart'
    as _i834;
import 'package:theadvance/src/presentation/collaborator/creating_task/bloc/creating_task_bloc.dart'
    as _i846;
import 'package:theadvance/src/presentation/collaborator/creating_task/selecting_office/bloc/selecting_office_bloc.dart'
    as _i837;
import 'package:theadvance/src/presentation/collaborator/creating_task/selecting_staff/bloc/selecting_staff_bloc.dart'
    as _i899;
import 'package:theadvance/src/presentation/collaborator/creating_task/sheet/bloc/repeat_task_bloc.dart'
    as _i415;
import 'package:theadvance/src/presentation/collaborator/customer/customer/bloc/customer_bloc.dart'
    as _i941;
import 'package:theadvance/src/presentation/collaborator/customer/function_room/bloc/function_room_bloc.dart'
    as _i900;
import 'package:theadvance/src/presentation/collaborator/customer/info_customer/bloc/info_customer_bloc.dart'
    as _i581;
import 'package:theadvance/src/presentation/collaborator/detail_eform/approval_otp/bloc/approving_otp_bloc.dart'
    as _i942;
import 'package:theadvance/src/presentation/collaborator/detail_eform/detail_eform/bloc/detail_eform_bloc.dart'
    as _i607;
import 'package:theadvance/src/presentation/collaborator/detail_eform/detail_eform/sheet/refuse_bottom_sheet/bloc/refuse_bloc.dart'
    as _i848;
import 'package:theadvance/src/presentation/collaborator/detail_eform/detail_eform/sheet/type_approving_bottom_sheet/bloc/type_approving_bloc.dart'
    as _i896;
import 'package:theadvance/src/presentation/collaborator/detail_job_scheduler/bloc/detail_job_scheduler_bloc.dart'
    as _i488;
import 'package:theadvance/src/presentation/collaborator/detail_job_scheduler/sheet/bloc/feedback_bloc.dart'
    as _i965;
import 'package:theadvance/src/presentation/collaborator/detail_news/bloc/detail_news_bloc.dart'
    as _i616;
import 'package:theadvance/src/presentation/collaborator/detail_notification/bloc/detail_notification_bloc.dart'
    as _i926;
import 'package:theadvance/src/presentation/collaborator/edit_home_menu/bloc/edit_home_menu_bloc.dart'
    as _i929;
import 'package:theadvance/src/presentation/collaborator/eform/bloc/eform_bloc.dart'
    as _i731;
import 'package:theadvance/src/presentation/collaborator/eform/sheet/eform_category_bottom_sheet/bloc/eform_category_bloc.dart'
    as _i878;
import 'package:theadvance/src/presentation/collaborator/history_checkin/bloc/history_checkin_bloc.dart'
    as _i979;
import 'package:theadvance/src/presentation/collaborator/home/<USER>/home_bloc.dart'
    as _i922;
import 'package:theadvance/src/presentation/collaborator/home_find/bloc/home_find_bloc.dart'
    as _i670;
import 'package:theadvance/src/presentation/collaborator/job_scheduler/bloc/job_scheduler_bloc.dart'
    as _i882;
import 'package:theadvance/src/presentation/collaborator/language/bloc/language_bloc.dart'
    as _i4;
import 'package:theadvance/src/presentation/collaborator/login/bloc/login_bloc.dart'
    as _i953;
import 'package:theadvance/src/presentation/collaborator/monthly_history_checkin/bloc/monthly_history_checkin_bloc.dart'
    as _i955;
import 'package:theadvance/src/presentation/collaborator/more/bloc/more_bloc.dart'
    as _i976;
import 'package:theadvance/src/presentation/collaborator/news/bloc/news_bloc.dart'
    as _i602;
import 'package:theadvance/src/presentation/collaborator/notifications/bloc/notifications_bloc.dart'
    as _i849;
import 'package:theadvance/src/presentation/collaborator/order_food/bloc/order_food_bloc.dart'
    as _i603;
import 'package:theadvance/src/presentation/collaborator/order_food/bloc/order_food_service_bloc.dart'
    as _i925;
import 'package:theadvance/src/presentation/collaborator/profile/bloc/profile_bloc.dart'
    as _i932;
import 'package:theadvance/src/presentation/collaborator/service_and_product/bloc/service_and_product_bloc.dart'
    as _i982;
import 'package:theadvance/src/presentation/collaborator/set_password/bloc/set_password_bloc.dart'
    as _i936;
import 'package:theadvance/src/presentation/collaborator/setting/bloc/setting_bloc.dart'
    as _i973;
import 'package:theadvance/src/presentation/collaborator/support_requests/bloc/support_requests_bloc.dart'
    as _i980;
import 'package:theadvance/src/presentation/collaborator/tabbar/bloc/tabbar_bloc.dart'
    as _i678;
import 'package:theadvance/src/presentation/comment_list/bloc/comment_list_bloc.dart'
    as _i883;
import 'package:theadvance/src/presentation/consultation_customer/bloc/consultation_customer_bloc.dart'
    as _i974;
import 'package:theadvance/src/presentation/consultation_customer/bloc/service_detail_bloc.dart'
    as _i968;
import 'package:theadvance/src/presentation/consultation_history/bloc/consultation_history_bloc.dart'
    as _i957;
import 'package:theadvance/src/presentation/consultation_history_detail/bloc/consultation_history_detail_bloc.dart'
    as _i961;
import 'package:theadvance/src/presentation/consultation_manager/bloc/consultation_manager_bloc.dart'
    as _i983;
import 'package:theadvance/src/presentation/create_chat_folder/bloc/create_chat_folder_bloc.dart'
    as _i553;
import 'package:theadvance/src/presentation/create_chat_group/bloc/create_chat_group_bloc.dart'
    as _i472;
import 'package:theadvance/src/presentation/create_customer/bloc/create_customer_bloc.dart'
    as _i934;
import 'package:theadvance/src/presentation/customer_booking_info/bloc/customer_booking_info_bloc.dart'
    as _i959;
import 'package:theadvance/src/presentation/customer_info_details/bloc/customer_info_details_bloc.dart'
    as _i952;
import 'package:theadvance/src/presentation/customer_list/bloc/customer_list_bloc.dart'
    as _i933;
import 'package:theadvance/src/presentation/customer_profile/bloc/customer_profile_bloc.dart'
    as _i958;
import 'package:theadvance/src/presentation/customer_record/bloc/customer_record_bloc.dart'
    as _i685;
import 'package:theadvance/src/presentation/customer_schedule/bloc/customer_schedule_bloc.dart'
    as _i940;
import 'package:theadvance/src/presentation/detail_crm_customer/bloc/detail_crm_customer_bloc.dart'
    as _i964;
import 'package:theadvance/src/presentation/detail_staff_evaluation_period/bloc/detail_staff_evaluation_period_bloc.dart'
    as _i669;
import 'package:theadvance/src/presentation/dev/bloc/dev_bloc.dart' as _i784;
import 'package:theadvance/src/presentation/group_chat_detail/bloc/group_chat_detail_bloc.dart'
    as _i812;
import 'package:theadvance/src/presentation/hr_organization/bloc/hr_organization_bloc.dart'
    as _i703;
import 'package:theadvance/src/presentation/important_notes/bloc/important_notes_bloc.dart'
    as _i679;
import 'package:theadvance/src/presentation/kpi_employee/bloc/kpi_employee_bloc.dart'
    as _i843;
import 'package:theadvance/src/presentation/like_list/bloc/like_list_bloc.dart'
    as _i747;
import 'package:theadvance/src/presentation/list_customer/bloc/list_customer_bloc.dart'
    as _i884;
import 'package:theadvance/src/presentation/medical_department_list/bloc/medical_department_list_bloc.dart'
    as _i631;
import 'package:theadvance/src/presentation/medical_log_detail/bloc/medical_log_detail_bloc.dart'
    as _i972;
import 'package:theadvance/src/presentation/medical_product_creation/bloc/medical_product_creation_bloc.dart'
    as _i967;
import 'package:theadvance/src/presentation/medical_service_creation/bloc/medical_service_creation_bloc.dart'
    as _i975;
import 'package:theadvance/src/presentation/medical_service_list/bloc/medical_service_list_bloc.dart'
    as _i487;
import 'package:theadvance/src/presentation/medical_service_log_list/bloc/medical_service_log_list_bloc.dart'
    as _i930;
import 'package:theadvance/src/presentation/medical_template_list/bloc/medical_template_list_bloc.dart'
    as _i897;
import 'package:theadvance/src/presentation/medicine_detail/bloc/medicine_detail_bloc.dart'
    as _i923;
import 'package:theadvance/src/presentation/note_details/bloc/note_details_bloc.dart'
    as _i821;
import 'package:theadvance/src/presentation/notification_list/bloc/notification_list_bloc.dart'
    as _i491;
import 'package:theadvance/src/presentation/product_confirm/bloc/product_confirm_bloc.dart'
    as _i898;
import 'package:theadvance/src/presentation/product_confirm/bloc/product_detail_confirm_bloc.dart'
    as _i785;
import 'package:theadvance/src/presentation/px_list/bloc/px_list_bloc.dart'
    as _i978;
import 'package:theadvance/src/presentation/px_recheck/bloc/px_recheck_bloc.dart'
    as _i977;
import 'package:theadvance/src/presentation/px_task_list/bloc/px_task_list_bloc.dart'
    as _i970;
import 'package:theadvance/src/presentation/px_unasigned/bloc/px_unasigned_bloc.dart'
    as _i963;
import 'package:theadvance/src/presentation/px_unasigned_update/bloc/px_unasigned_update_bloc.dart'
    as _i966;
import 'package:theadvance/src/presentation/rating_human/bloc/rating_human_bloc.dart'
    as _i960;
import 'package:theadvance/src/presentation/schedule_details/bloc/schedule_details_bloc.dart'
    as _i787;
import 'package:theadvance/src/presentation/select_px_room/bloc/select_px_room_bloc.dart'
    as _i913;
import 'package:theadvance/src/presentation/settings/fonts/fonts_bloc.dart'
    as _i489;
import 'package:theadvance/src/presentation/settings/multi_language/multi_language_bloc.dart'
    as _i730;
import 'package:theadvance/src/presentation/settings/theme/theme_bloc.dart'
    as _i671;
import 'package:theadvance/src/presentation/staff_evaluation_periods/bloc/staff_evaluation_periods_bloc.dart'
    as _i836;
import 'package:theadvance/src/presentation/story_detail/bloc/story_detail_bloc.dart'
    as _i825;
import 'package:theadvance/src/presentation/story_list/bloc/sticker_bloc.dart'
    as _i820;
import 'package:theadvance/src/presentation/story_list/bloc/story_list_bloc.dart'
    as _i885;
import 'package:theadvance/src/presentation/story_list/bloc/story_write_bloc.dart'
    as _i938;
import 'package:theadvance/src/presentation/story_person_list/bloc/story_person_list_bloc.dart'
    as _i490;
import 'package:theadvance/src/presentation/tag_by_result_image_list/bloc/image_by_combo_tag_bloc.dart'
    as _i956;
import 'package:theadvance/src/presentation/tag_image/bloc/tag_image_bloc.dart'
    as _i954;
import 'package:theadvance/src/presentation/taking_care_customer/bloc/taking_care_customer_bloc.dart'
    as _i822;
import 'package:theadvance/src/presentation/ticket/bloc/ticket_bloc.dart'
    as _i924;
import 'package:theadvance/src/presentation/ticket_detail/bloc/ticket_active_bloc.dart'
    as _i748;
import 'package:theadvance/src/presentation/ticket_detail/bloc/ticket_detail_bloc.dart'
    as _i935;
import 'package:theadvance/src/presentation/user_list/bloc/user_list_bloc.dart'
    as _i844;
import 'package:theadvance/src/presentation/user_ticket/bloc/drop_down_status_bloc.dart'
    as _i886;
import 'package:theadvance/src/presentation/user_ticket/bloc/user_ticket_bloc.dart'
    as _i931;
import 'package:theadvance/src/presentation/widgets/user_profile/bloc/user_profile_bloc.dart'
    as _i943;

extension GetItInjectableX on _i1.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i1.GetIt init({
    String? environment,
    _i2.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i2.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final registerModule = _$RegisterModule();
    gh.factory<_i3.EventAuthenticationBloc>(
        () => _i3.EventAuthenticationBloc());
    gh.factory<_i4.LanguageBloc>(() => _i4.LanguageBloc());
    gh.lazySingleton<_i5.StringeeHelper>(() => _i5.StringeeHelper());
    gh.lazySingleton<_i6.Mapper>(() => _i6.Mapper());
    gh.lazySingleton<_i7.DeeplinkHelper>(() => _i7.DeeplinkHelper());
    gh.lazySingleton<_i8.DefaultCacheManager>(
        () => registerModule.cacheManager);
    gh.lazySingleton<_i9.EZCache>(() => registerModule.collaboratorCache);
    gh.lazySingleton<_i10.AppRouter>(() => registerModule.appRouter);
    gh.factory<_i11.Dio>(
      () => registerModule.collaboratorStaticApiDio,
      instanceName: 'StaticApiDio',
    );
    gh.factory<_i11.Dio>(
      () => registerModule.collaboratorApiSocialDio,
      instanceName: 'ApiSocialDio',
    );
    gh.factory<_i11.Dio>(
      () => registerModule.collaboratorApiDio,
      instanceName: 'ApiDio',
    );
    gh.factory<String>(
      () => registerModule.collaboratorStaticApiBaseUrl,
      instanceName: 'StaticApiBaseUrl',
    );
    gh.factory<String>(
      () => registerModule.collaboratorApiSocialBaseUrl,
      instanceName: 'ApiSocialBaseUrl',
    );
    gh.factory<String>(
      () => registerModule.googleMapApiBaseUrl,
      instanceName: 'ApiGoogleMapBaseUrl',
    );
    gh.lazySingleton<_i12.TagListDao>(() => _i13.TagListDaoImpl());
    gh.factory<_i11.Dio>(
      () => registerModule.aIApiDio,
      instanceName: 'ApiAiDio',
    );
    gh.factory<_i11.Dio>(
      () => registerModule.googleMapApiDio,
      instanceName: 'ApiGoogleMapDio',
    );
    gh.factory<_i11.Dio>(
      () => registerModule.customerStaticApiDio,
      instanceName: 'StaticCustomerApiDio',
    );
    gh.factory<String>(
      () => registerModule.aIApiBaseUrl,
      instanceName: 'ApiAiBaseUrl',
    );
    gh.factory<String>(
      () => registerModule.customerStaticApiBaseUrl,
      instanceName: 'StaticCustomerApiBaseUrl',
    );
    gh.lazySingleton<_i14.StoryListApiService>(
        () => registerModule.storyListApiService(
              gh<_i11.Dio>(instanceName: 'ApiSocialDio'),
              gh<String>(instanceName: 'ApiSocialBaseUrl'),
            ));
    gh.lazySingleton<_i15.CommentListApiService>(
        () => registerModule.commentListApiService(
              gh<_i11.Dio>(instanceName: 'ApiSocialDio'),
              gh<String>(instanceName: 'ApiSocialBaseUrl'),
            ));
    gh.lazySingleton<_i16.LikeListApiService>(
        () => registerModule.likeListApiService(
              gh<_i11.Dio>(instanceName: 'ApiSocialDio'),
              gh<String>(instanceName: 'ApiSocialBaseUrl'),
            ));
    gh.lazySingleton<_i17.StoryPersonListApiService>(
        () => registerModule.storyPersonListApiService(
              gh<_i11.Dio>(instanceName: 'ApiSocialDio'),
              gh<String>(instanceName: 'ApiSocialBaseUrl'),
            ));
    gh.lazySingleton<_i18.TagListApiService>(
        () => registerModule.tagListApiService(
              gh<_i11.Dio>(instanceName: 'ApiSocialDio'),
              gh<String>(instanceName: 'ApiSocialBaseUrl'),
            ));
    gh.lazySingleton<_i18.StoryDetailApiService>(
        () => registerModule.storyDetailApiService(
              gh<_i11.Dio>(instanceName: 'ApiSocialDio'),
              gh<String>(instanceName: 'ApiSocialBaseUrl'),
            ));
    gh.lazySingleton<_i18.NotificationListApiService>(
        () => registerModule.notificationListApiService(
              gh<_i11.Dio>(instanceName: 'ApiSocialDio'),
              gh<String>(instanceName: 'ApiSocialBaseUrl'),
            ));
    gh.factory<String>(
      () => registerModule.collaboratorApiBaseUrl,
      instanceName: 'ApiBaseUrl',
    );
    gh.lazySingleton<_i19.MedicalServiceCreationDao>(
        () => _i20.MedicalServiceCreationDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i21.PxTaskListDao>(
        () => _i22.PxTaskListDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i24.LocationGoogleDao>(
        () => _i25.LocationGoogleDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i26.GroupChatDetailDao>(
        () => _i27.GroupChatDetailDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i28.CheckinPhotoDao>(
        () => _i29.CheckinPhotoDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i30.MedicalLogDetailDao>(
        () => _i31.MedicalLogDetailDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i32.PxRecheckDao>(
        () => _i33.PxRecheckDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i34.HrOrganizationDao>(
        () => _i35.HrOrganizationDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i36.TakingCareCustomerDao>(
        () => _i37.TakingCareCustomerDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i38.PxListDao>(
        () => _i39.PxListDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i40.ChatListRepository>(
        () => _i41.ChatListRepositoryImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i42.BranchSelectionDao>(
        () => _i43.BranchSelectionDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i44.CreateChatGroupRepository>(
        () => _i45.CreateChatGroupRepositoryImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i46.ServiceAndProductDao>(
        () => _i47.ServiceAndProductDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i48.PxUnasignedDao>(
        () => _i49.PxUnasignedDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i50.MedicalProductCreationDao>(
        () => _i51.MedicalProductCreationDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i52.CustomerListDao>(
        () => _i53.CustomerListDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i54.MedicalServiceLogListDao>(
        () => _i55.MedicalServiceLogListDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i56.CustomerScheduleDao>(
        () => _i57.CustomerScheduleDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i58.DetailCrmCustomerDao>(
        () => _i59.DetailCrmCustomerDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i60.BranchChatListDao>(
        () => _i61.BranchChatListDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i62.CreateChatFolderDao>(
        () => _i63.CreateChatFolderDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i64.FeedbackDao>(
        () => _i65.FeedbackDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i66.TrackLoginUseCase>(
        () => _i66.TrackLoginUseCase(gh<_i67.TrackingRepository>()));
    gh.lazySingleton<_i68.TrackValidInstallUseCase>(
        () => _i68.TrackValidInstallUseCase(gh<_i67.TrackingRepository>()));
    gh.lazySingleton<_i69.TrackOpenLandingPageUseCase>(
        () => _i69.TrackOpenLandingPageUseCase(gh<_i67.TrackingRepository>()));
    gh.lazySingleton<_i70.TrackBannerUseCase>(
        () => _i70.TrackBannerUseCase(gh<_i67.TrackingRepository>()));
    gh.lazySingleton<_i71.TrackOpenAppUseCase>(
        () => _i71.TrackOpenAppUseCase(gh<_i67.TrackingRepository>()));
    gh.lazySingleton<_i72.SendEventUseCase>(
        () => _i72.SendEventUseCase(gh<_i67.TrackingRepository>()));
    gh.lazySingleton<_i73.TrackPopupUseCase>(
        () => _i73.TrackPopupUseCase(gh<_i67.TrackingRepository>()));
    gh.lazySingleton<_i74.TrackRegisterUseCase>(
        () => _i74.TrackRegisterUseCase(gh<_i67.TrackingRepository>()));
    gh.lazySingleton<_i75.TrackNotificationUseCase>(
        () => _i75.TrackNotificationUseCase(gh<_i67.TrackingRepository>()));
    gh.lazySingleton<_i76.PxUnasignedUpdateDao>(
        () => _i77.PxUnasignedUpdateDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i78.AssignTaskDao>(
        () => _i79.AssignTaskDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i80.ConsultationCustomerDao>(
        () => _i81.ConsultationCustomerDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i82.MedicalServiceListDao>(
        () => _i83.MedicalServiceListDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i84.DevDao>(() => _i85.DevDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i9.StaticApiService>(
        () => registerModule.collaboratorStaticApiService(
              gh<_i11.Dio>(instanceName: 'StaticApiDio'),
              gh<String>(instanceName: 'StaticApiBaseUrl'),
            ));
    gh.lazySingleton<_i86.MedicalDepartmentListDao>(
        () => _i87.MedicalDepartmentListDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i88.NotificationListRepository>(
        () => _i89.NotificationListRepositoryImpl(
              gh<_i18.NotificationListApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i90.CustomerProfileDao>(
        () => _i91.CustomerProfileDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i92.ChatListDao>(
        () => _i93.ChatListDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i94.ConsultationManagerDao>(
        () => _i95.ConsultationManagerDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i96.CreateCustomerDao>(
        () => _i97.CreateCustomerDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i98.CustomerBookingInfoDao>(
        () => _i99.CustomerBookingInfoDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i100.NoteDetailsDao>(
        () => _i101.NoteDetailsDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i102.CreateChatGroupDao>(
        () => _i103.CreateChatGroupDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i104.ProductConfirmDao>(
        () => _i105.ProductConfirmDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i106.ListCustomerDao>(
        () => _i107.ListCustomerDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i108.CustomerRecordDao>(
        () => _i109.CustomerRecordDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i110.MedicineDetailDao>(
        () => _i111.MedicineDetailDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i112.ChatSelectBranchDao>(
        () => _i113.ChatSelectBranchDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i114.StoryDetailDao>(
        () => _i115.StoryDetailDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i116.ScheduleDetailsDao>(
        () => _i117.ScheduleDetailsDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i118.ImportantNotesDao>(
        () => _i119.ImportantNotesDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i120.MedicalTemplateListDao>(
        () => _i121.MedicalTemplateListDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i122.StoryDetailRepository>(
        () => _i123.StoryDetailRepositoryImpl(
              gh<_i18.StoryDetailApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i124.CreateChatFolderRepository>(
        () => _i125.CreateChatFolderRepositoryImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i126.UserListDao>(
        () => _i127.UserListDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i128.HelperRepository>(
        () => _i129.HelperRepositoryImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i130.SelectPxRoomDao>(
        () => _i131.SelectPxRoomDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i132.CustomerInfoDetailsDao>(
        () => _i133.CustomerInfoDetailsDaoImpl(gh<_i9.EZCache>()));
    gh.lazySingleton<_i134.NotificationListDao>(
        () => _i135.NotificationListDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i136.StaffEvaluationPeriodsDao>(
        () => _i137.StaffEvaluationPeriodsDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i138.DetailStaffEvaluationPeriodDao>(
        () => _i139.DetailStaffEvaluationPeriodDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i140.SettingRepository>(
        () => _i141.SettingRepositoryImpl(cache: gh<_i9.EZCache>()));
    gh.lazySingleton<_i142.MediaUploadApiService>(
        () => registerModule.recordApiService(
              gh<_i11.Dio>(instanceName: 'StaticCustomerApiDio'),
              gh<String>(instanceName: 'StaticCustomerApiBaseUrl'),
            ));
    gh.lazySingleton<_i143.ChatApiService>(() => registerModule.chatApiService(
          gh<_i11.Dio>(instanceName: 'StaticCustomerApiDio'),
          gh<String>(instanceName: 'StaticCustomerApiBaseUrl'),
        ));
    gh.lazySingleton<_i144.SocialApiService>(
        () => registerModule.socialApiService(
              gh<_i11.Dio>(instanceName: 'StaticCustomerApiDio'),
              gh<String>(instanceName: 'StaticCustomerApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.GroupChatDetailApiService>(
        () => registerModule.groupChatDetailApiService(
              gh<_i11.Dio>(instanceName: 'StaticCustomerApiDio'),
              gh<String>(instanceName: 'StaticCustomerApiBaseUrl'),
            ));
    gh.lazySingleton<_i145.StickerSocialApiService>(
        () => registerModule.stickerSocialApiService(
              gh<_i11.Dio>(instanceName: 'StaticCustomerApiDio'),
              gh<String>(instanceName: 'StaticCustomerApiBaseUrl'),
            ));
    gh.lazySingleton<_i146.TicketDetailDao>(
        () => _i147.TicketDetailDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i148.ChatDao>(
        () => _i149.ChatDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i150.TagListRepository>(
        () => _i151.TagListRepositoryImpl(gh<_i18.TagListApiService>()));
    gh.factory<_i152.SortFolderChatListUseCase>(
        () => _i152.SortFolderChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.factory<_i153.JoinGroupChatListUseCase>(
        () => _i153.JoinGroupChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.factory<_i154.GetTotalUnreadChatListUseCase>(() =>
        _i154.GetTotalUnreadChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.factory<_i155.GetSavedChatListUseCase>(
        () => _i155.GetSavedChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.factory<_i156.PinConversationChatListUseCase>(() =>
        _i156.PinConversationChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.factory<_i157.SearchChatListUseCase>(
        () => _i157.SearchChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.factory<_i158.MarkAsReadChatListUseCase>(
        () => _i158.MarkAsReadChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.factory<_i159.SearchMessageChatListUseCase>(() =>
        _i159.SearchMessageChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.factory<_i160.GetConversationByInviteIdChatListUseCase>(() =>
        _i160.GetConversationByInviteIdChatListUseCase(
            gh<_i40.ChatListRepository>()));
    gh.factory<_i161.GetRecentContactsChatListUseCase>(() =>
        _i161.GetRecentContactsChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.factory<_i162.GetChatListUseCase>(
        () => _i162.GetChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.factory<_i163.UpdatePinConversationChatListUseCase>(() =>
        _i163.UpdatePinConversationChatListUseCase(
            gh<_i40.ChatListRepository>()));
    gh.factory<_i164.RemoveChatListUseCase>(
        () => _i164.RemoveChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.factory<_i165.SaveChatListUseCase>(
        () => _i165.SaveChatListUseCase(gh<_i40.ChatListRepository>()));
    gh.lazySingleton<_i166.StoryPersonListRepository>(() =>
        _i167.StoryPersonListRepositoryImpl(
            gh<_i17.StoryPersonListApiService>()));
    gh.lazySingleton<_i168.StoryListRepository>(
        () => _i169.StoryListRepositoryImpl(
              gh<_i14.StoryListApiService>(),
              gh<_i144.SocialApiService>(),
            ));
    gh.lazySingleton<_i18.LocationGoogleApiService>(
        () => registerModule.locationGoogleApiService(
              gh<_i11.Dio>(instanceName: 'ApiGoogleMapDio'),
              gh<String>(instanceName: 'ApiGoogleMapBaseUrl'),
            ));
    gh.lazySingleton<_i170.GroupChatDetailRepository>(
        () => _i171.GroupChatDetailRepositoryImpl(
              gh<_i18.GroupChatDetailApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i172.RemoveStoryDetailUseCase>(() =>
        _i172.RemoveStoryDetailUseCase(gh<_i122.StoryDetailRepository>()));
    gh.factory<_i173.GetSavedStoryDetailUseCase>(() =>
        _i173.GetSavedStoryDetailUseCase(gh<_i122.StoryDetailRepository>()));
    gh.factory<_i174.GetStoryDetailUseCase>(
        () => _i174.GetStoryDetailUseCase(gh<_i122.StoryDetailRepository>()));
    gh.factory<_i175.SaveStoryDetailUseCase>(
        () => _i175.SaveStoryDetailUseCase(gh<_i122.StoryDetailRepository>()));
    gh.lazySingleton<_i176.ChatRepository>(() => _i177.ChatRepositoryImpl(
          gh<_i23.EZCache>(),
          gh<_i143.ChatApiService>(),
        ));
    gh.factory<_i178.GetSavedCreateChatGroupUseCase>(() =>
        _i178.GetSavedCreateChatGroupUseCase(
            gh<_i44.CreateChatGroupRepository>()));
    gh.factory<_i179.SaveCreateChatGroupUseCase>(() =>
        _i179.SaveCreateChatGroupUseCase(gh<_i44.CreateChatGroupRepository>()));
    gh.factory<_i180.UserLoadCreateChatGroupUseCase>(() =>
        _i180.UserLoadCreateChatGroupUseCase(
            gh<_i44.CreateChatGroupRepository>()));
    gh.factory<_i181.GetCreateChatGroupUseCase>(() =>
        _i181.GetCreateChatGroupUseCase(gh<_i44.CreateChatGroupRepository>()));
    gh.factory<_i182.RemoveCreateChatGroupUseCase>(() =>
        _i182.RemoveCreateChatGroupUseCase(
            gh<_i44.CreateChatGroupRepository>()));
    gh.lazySingleton<_i183.StickerSocailRepository>(
        () => _i184.StickerSocialRepositoryImpl(
              gh<_i145.StickerSocialApiService>(),
              gh<_i144.SocialApiService>(),
            ));
    gh.lazySingleton<_i9.EformApiService>(() => registerModule.eformApiService(
          gh<_i11.Dio>(instanceName: 'ApiDio'),
          gh<String>(instanceName: 'ApiBaseUrl'),
        ));
    gh.lazySingleton<_i9.UserApiService>(
        () => registerModule.collaboratorUserApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.StaffApiService>(() => registerModule.staffApiService(
          gh<_i11.Dio>(instanceName: 'ApiDio'),
          gh<String>(instanceName: 'ApiBaseUrl'),
        ));
    gh.lazySingleton<_i9.HomeApiService>(
        () => registerModule.collaboratorHomeApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.CustomerApiService>(
        () => registerModule.collaboratorCustomerApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.CheckinApiService>(
        () => registerModule.checkinApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.NewsApiService>(
        () => registerModule.collaboratornewsApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.NotificationApiService>(
        () => registerModule.collaboratorNotificationApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.TaskApiService>(() => registerModule.taskApiService(
          gh<_i11.Dio>(instanceName: 'ApiDio'),
          gh<String>(instanceName: 'ApiBaseUrl'),
        ));
    gh.lazySingleton<_i9.RequestApiService>(
        () => registerModule.collaboratorRequestApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.TrackingApiService>(
        () => registerModule.trackingApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.BranchSelectionApiService>(
        () => registerModule.branchSelectionApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.ImportantNotesApiService>(
        () => registerModule.importantNotesApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.CustomerProfileApiService>(
        () => registerModule.customerProfileApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.CustomerInfoDetailsApiService>(
        () => registerModule.customerInfoDetailsApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.NoteDetailsApiService>(
        () => registerModule.noteDetailsApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.MedicalDepartmentListApiService>(
        () => registerModule.medicalDepartmentListApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.ListCustomerApiService>(
        () => registerModule.listCustomerApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.ServiceAndProductApiService>(
        () => registerModule.serviceAndProductApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.MedicalServiceLogListApiService>(
        () => registerModule.medicalServiceLogListApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.MedicalLogDetailApiService>(
        () => registerModule.medicalLogDetailApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.MedicineDetailApiService>(
        () => registerModule.medicineDetailApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.MedicalTemplateListApiService>(
        () => registerModule.medicalTemplateListApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.MedicalProductCreationApiService>(
        () => registerModule.medicalProductCreationApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.MedicalServiceCreationApiService>(
        () => registerModule.medicalServiceCreationApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i9.FoodApiService>(() => registerModule.foodApiService(
          gh<_i11.Dio>(instanceName: 'ApiDio'),
          gh<String>(instanceName: 'ApiBaseUrl'),
        ));
    gh.lazySingleton<_i18.ScheduleDetailsApiService>(
        () => registerModule.scheduleDetailsApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.CustomerScheduleApiService>(
        () => registerModule.customerScheduleApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.CustomerBookingInfoApiService>(
        () => registerModule.customerBookingInfoApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.AssignTaskApiService>(
        () => registerModule.assignTaskApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.ChatSelectBranchApiService>(
        () => registerModule.chatSelectBranchApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.BranchChatListApiService>(
        () => registerModule.branchChatListApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.PxListApiService>(
        () => registerModule.pxListApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.PxUnasignedApiService>(
        () => registerModule.pxUnasignedApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.PxTaskListApiService>(
        () => registerModule.pxTaskListApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.PxUnasignedUpdateApiService>(
        () => registerModule.pxUnasignedUpdateApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.TakingCareCustomerApiService>(
        () => registerModule.takingCareCustomerApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.PxRecheckApiService>(
        () => registerModule.pxRecheckApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.CreateCustomerApiService>(
        () => registerModule.createCustomerApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.SelectPxRoomApiService>(
        () => registerModule.selectPxRoomApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.CustomerListApiService>(
        () => registerModule.customerListApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.ConsultationManagerApiService>(
        () => registerModule.consultationManagerApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.ConsultationCustomerApiService>(
        () => registerModule.consultationCustomerApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.StaffEvaluationPeriodsApiService>(
        () => registerModule.staffEvaluationPeriodsApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.DetailStaffEvaluationPeriodApiService>(
        () => registerModule.detailStaffEvaluationPeriodApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i185.RatingHumanApiService>(
        () => registerModule.ratingHumanApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i186.NoteListApiService>(
        () => registerModule.noteListApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.DetailCrmCustomerApiService>(
        () => registerModule.detailCrmCustomerApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.HrOrganizationApiService>(
        () => registerModule.hrOrganizationApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.CustomerRecordApiService>(
        () => registerModule.customerRecordApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.CheckinPhotoApiService>(
        () => registerModule.checkinPhotoApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.FeedbackApiService>(
        () => registerModule.feedbackApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i187.CreateChatGroupApiService>(
        () => registerModule.createChatGroupApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.UserListApiService>(
        () => registerModule.userListApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.CreateChatFolderApiService>(
        () => registerModule.createChatFolderApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i188.TicketApiService>(
        () => registerModule.ticketApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.TicketDetailApiService>(
        () => registerModule.ticketDetailApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i189.TicketActiveApiService>(
        () => registerModule.ticketActiveApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.DevApiService>(() => registerModule.devApiService(
          gh<_i11.Dio>(instanceName: 'ApiDio'),
          gh<String>(instanceName: 'ApiBaseUrl'),
        ));
    gh.lazySingleton<_i18.KpiEmployeeApiService>(
        () => registerModule.kpiEmployeeApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i18.ProductConfirmApiService>(
        () => registerModule.productConfirmApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i190.TagImageApiService>(
        () => registerModule.tagImageApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.factory<_i18.MedicalServiceListApiService>(
        () => registerModule.medicalServiceListApiService(
              gh<_i11.Dio>(instanceName: 'ApiDio'),
              gh<String>(instanceName: 'ApiBaseUrl'),
            ));
    gh.lazySingleton<_i191.SelectPxRoomRepository>(
        () => _i192.SelectPxRoomRepositoryImpl(
              gh<_i18.SelectPxRoomApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i193.LikeListRepository>(
        () => _i194.LikeListRepositoryImpl(gh<_i16.LikeListApiService>()));
    gh.lazySingleton<_i195.FoodRepository>(() => _i196.FoodRepositoryImpl(
          gh<_i9.FoodApiService>(),
          gh<_i142.MediaUploadApiService>(),
        ));
    gh.lazySingleton<_i197.BranchSelectionRepository>(
        () => _i198.BranchSelectionRepositoryImpl(
              gh<_i9.BranchSelectionApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.lazySingleton<_i199.TicketActiveRepository>(() =>
        _i200.TicketActiveRepositoryImpl(gh<_i189.TicketActiveApiService>()));
    gh.lazySingleton<_i201.MedicalDepartmentListRepository>(
        () => _i202.MedicalDepartmentListRepositoryImpl(
              gh<_i9.MedicalDepartmentListApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.lazySingleton<_i203.DetailStaffEvaluationPeriodRepository>(
        () => _i204.DetailStaffEvaluationPeriodRepositoryImpl(
              gh<_i18.DetailStaffEvaluationPeriodApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i205.ImportantNotesRepository>(
        () => _i206.ImportantNotesRepositoryImpl(
              gh<_i18.ImportantNotesApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.lazySingleton<_i207.UserTickerRepository>(() =>
        _i208.StaffEvaluationPeriodsRepositoryImpl(
            gh<_i186.NoteListApiService>()));
    gh.lazySingleton<_i209.MedicineDetailRepository>(
        () => _i210.MedicineDetailRepositoryImpl(
              gh<_i18.MedicineDetailApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.factory<_i211.GetSavedChatUseCase>(
        () => _i211.GetSavedChatUseCase(gh<_i176.ChatRepository>()));
    gh.factory<_i212.MessageEditChatUseCase>(
        () => _i212.MessageEditChatUseCase(gh<_i176.ChatRepository>()));
    gh.factory<_i213.GetUserSeenChatUseCase>(
        () => _i213.GetUserSeenChatUseCase(gh<_i176.ChatRepository>()));
    gh.factory<_i214.TranscribeChatUseCase>(
        () => _i214.TranscribeChatUseCase(gh<_i176.ChatRepository>()));
    gh.factory<_i215.SearchChatUseCase>(
        () => _i215.SearchChatUseCase(gh<_i176.ChatRepository>()));
    gh.factory<_i216.GetConversationChatUseCase>(
        () => _i216.GetConversationChatUseCase(gh<_i176.ChatRepository>()));
    gh.factory<_i217.GetPinListChatUseCase>(
        () => _i217.GetPinListChatUseCase(gh<_i176.ChatRepository>()));
    gh.factory<_i218.SaveChatUseCase>(
        () => _i218.SaveChatUseCase(gh<_i176.ChatRepository>()));
    gh.factory<_i219.VotePollChatUseCase>(
        () => _i219.VotePollChatUseCase(gh<_i176.ChatRepository>()));
    gh.factory<_i220.GetUserStickerChatUseCase>(
        () => _i220.GetUserStickerChatUseCase(gh<_i176.ChatRepository>()));
    gh.factory<_i221.UploadFileChatUseCase>(
        () => _i221.UploadFileChatUseCase(gh<_i176.ChatRepository>()));
    gh.factory<_i222.UnpinMessageChatUseCase>(
        () => _i222.UnpinMessageChatUseCase(gh<_i176.ChatRepository>()));
    gh.factory<_i223.ReplyBotMessageChatUseCase>(
        () => _i223.ReplyBotMessageChatUseCase(gh<_i176.ChatRepository>()));
    gh.factory<_i224.ReactChatUseCase>(
        () => _i224.ReactChatUseCase(gh<_i176.ChatRepository>()));
    gh.factory<_i225.GetConversationByIdUseCase>(
        () => _i225.GetConversationByIdUseCase(gh<_i176.ChatRepository>()));
    gh.factory<_i226.SendChatUseCase>(
        () => _i226.SendChatUseCase(gh<_i176.ChatRepository>()));
    gh.factory<_i227.PinMessageChatUseCase>(
        () => _i227.PinMessageChatUseCase(gh<_i176.ChatRepository>()));
    gh.factory<_i228.UpdatePollChatUseCase>(
        () => _i228.UpdatePollChatUseCase(gh<_i176.ChatRepository>()));
    gh.factory<_i229.GetChatUseCase>(
        () => _i229.GetChatUseCase(gh<_i176.ChatRepository>()));
    gh.factory<_i230.MessageRemoveChatUseCase>(
        () => _i230.MessageRemoveChatUseCase(gh<_i176.ChatRepository>()));
    gh.factory<_i231.RemoveChatUseCase>(
        () => _i231.RemoveChatUseCase(gh<_i176.ChatRepository>()));
    gh.factory<_i232.ConversationDetailsUpdateChatUseCase>(() =>
        _i232.ConversationDetailsUpdateChatUseCase(gh<_i176.ChatRepository>()));
    gh.factory<_i233.CacheTextScaleRemoveUseCase>(
        () => _i233.CacheTextScaleRemoveUseCase(gh<_i128.HelperRepository>()));
    gh.factory<_i234.CacheTextScaleSaveUseCase>(
        () => _i234.CacheTextScaleSaveUseCase(gh<_i128.HelperRepository>()));
    gh.factory<_i235.SaveDeviceInfoUseCase>(
        () => _i235.SaveDeviceInfoUseCase(gh<_i128.HelperRepository>()));
    gh.factory<_i236.SaveLatitudeUseCase>(
        () => _i236.SaveLatitudeUseCase(gh<_i128.HelperRepository>()));
    gh.factory<_i237.DeleteLongitudeUseCase>(
        () => _i237.DeleteLongitudeUseCase(gh<_i128.HelperRepository>()));
    gh.factory<_i238.DeleteLatitudeUseCase>(
        () => _i238.DeleteLatitudeUseCase(gh<_i128.HelperRepository>()));
    gh.factory<_i239.GetKeyAppsFlyerUseCase>(
        () => _i239.GetKeyAppsFlyerUseCase(gh<_i128.HelperRepository>()));
    gh.factory<_i240.SaveAppVersionUseCase>(
        () => _i240.SaveAppVersionUseCase(gh<_i128.HelperRepository>()));
    gh.factory<_i241.SaveLongitudeUseCase>(
        () => _i241.SaveLongitudeUseCase(gh<_i128.HelperRepository>()));
    gh.factory<_i242.SaveKeyAppflyerUseCase>(
        () => _i242.SaveKeyAppflyerUseCase(gh<_i128.HelperRepository>()));
    gh.lazySingleton<_i243.CacheTextScaleGetUseCase>(
        () => _i243.CacheTextScaleGetUseCase(gh<_i128.HelperRepository>()));
    gh.lazySingleton<_i244.GetPhoneUseCase>(
        () => _i244.GetPhoneUseCase(gh<_i128.HelperRepository>()));
    gh.lazySingleton<_i245.GetAppVersionUseCase>(
        () => _i245.GetAppVersionUseCase(gh<_i128.HelperRepository>()));
    gh.lazySingleton<_i246.GetLatitudeUseCase>(
        () => _i246.GetLatitudeUseCase(gh<_i128.HelperRepository>()));
    gh.lazySingleton<_i247.GetAccessTokenUseCase>(
        () => _i247.GetAccessTokenUseCase(gh<_i128.HelperRepository>()));
    gh.lazySingleton<_i248.GetFirebaseTokenUseCase>(
        () => _i248.GetFirebaseTokenUseCase(gh<_i128.HelperRepository>()));
    gh.lazySingleton<_i249.GetLongitudeUseCase>(
        () => _i249.GetLongitudeUseCase(gh<_i128.HelperRepository>()));
    gh.lazySingleton<_i250.GetDeviceInfoUseCase>(
        () => _i250.GetDeviceInfoUseCase(gh<_i128.HelperRepository>()));
    gh.lazySingleton<_i251.CheckinPhotoRepository>(
        () => _i252.CheckinPhotoRepositoryImpl(
              gh<_i18.CheckinPhotoApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i253.PxTaskListRepository>(
        () => _i254.PxTaskListRepositoryImpl(
              gh<_i18.PxTaskListApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i255.TicketRepository>(() => _i256.TicketRepositoryImpl(
          gh<_i188.TicketApiService>(),
          gh<_i142.MediaUploadApiService>(),
        ));
    gh.lazySingleton<_i257.RatingHumanRepository>(() =>
        _i258.RatingHumanRepositoryImpl(gh<_i185.RatingHumanApiService>()));
    gh.factory<_i259.RemoveNotificationListUseCase>(() =>
        _i259.RemoveNotificationListUseCase(
            gh<_i88.NotificationListRepository>()));
    gh.factory<_i260.GetNotificationListUseCase>(() =>
        _i260.GetNotificationListUseCase(
            gh<_i88.NotificationListRepository>()));
    gh.factory<_i261.PutReadAllSocialUseCase>(() =>
        _i261.PutReadAllSocialUseCase(gh<_i88.NotificationListRepository>()));
    gh.factory<_i262.DeleteNotificationSocialUseCase>(() =>
        _i262.DeleteNotificationSocialUseCase(
            gh<_i88.NotificationListRepository>()));
    gh.factory<_i263.SaveNotificationListUseCase>(() =>
        _i263.SaveNotificationListUseCase(
            gh<_i88.NotificationListRepository>()));
    gh.factory<_i264.GetSavedNotificationListUseCase>(() =>
        _i264.GetSavedNotificationListUseCase(
            gh<_i88.NotificationListRepository>()));
    gh.lazySingleton<_i265.UserRepository>(() => _i266.UserRepositoryImpl(
          gh<_i9.UserApiService>(),
          gh<_i9.StaticApiService>(),
          gh<_i9.EZCache>(),
        ));
    gh.lazySingleton<_i267.ConsultationCustomerRepository>(
        () => _i268.ConsultationCustomerRepositoryImpl(
              gh<_i18.ConsultationCustomerApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i269.GetTagListUseCase>(
        () => _i269.GetTagListUseCase(gh<_i150.TagListRepository>()));
    gh.lazySingleton<_i270.FeedbackRepository>(
        () => _i271.FeedbackRepositoryImpl(
              gh<_i18.FeedbackApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i272.GetUserExceptionGroupChatDetailUseCase>(() =>
        _i272.GetUserExceptionGroupChatDetailUseCase(
            gh<_i170.GroupChatDetailRepository>()));
    gh.factory<_i273.AvatarUploadGroupChatDetailUseCase>(() =>
        _i273.AvatarUploadGroupChatDetailUseCase(
            gh<_i170.GroupChatDetailRepository>()));
    gh.factory<_i274.RemoveGroupChatDetailUseCase>(() =>
        _i274.RemoveGroupChatDetailUseCase(
            gh<_i170.GroupChatDetailRepository>()));
    gh.factory<_i275.MemberInfoLoadGroupChatDetailUseCase>(() =>
        _i275.MemberInfoLoadGroupChatDetailUseCase(
            gh<_i170.GroupChatDetailRepository>()));
    gh.factory<_i276.DeleteGroupUseCase>(
        () => _i276.DeleteGroupUseCase(gh<_i170.GroupChatDetailRepository>()));
    gh.factory<_i277.MediaLoadGroupChatDetailUseCase>(() =>
        _i277.MediaLoadGroupChatDetailUseCase(
            gh<_i170.GroupChatDetailRepository>()));
    gh.factory<_i278.UpdateMemberRuleGroupChatDetailUseCase>(() =>
        _i278.UpdateMemberRuleGroupChatDetailUseCase(
            gh<_i170.GroupChatDetailRepository>()));
    gh.factory<_i279.GetGroupChatDetailUseCase>(() =>
        _i279.GetGroupChatDetailUseCase(gh<_i170.GroupChatDetailRepository>()));
    gh.factory<_i280.ChangeOwnerGroupChatDetailUseCase>(() =>
        _i280.ChangeOwnerGroupChatDetailUseCase(
            gh<_i170.GroupChatDetailRepository>()));
    gh.factory<_i281.LinkLoadGroupChatDetailUseCase>(() =>
        _i281.LinkLoadGroupChatDetailUseCase(
            gh<_i170.GroupChatDetailRepository>()));
    gh.factory<_i282.UpdateAdminRuleGroupChatDetailUseCase>(() =>
        _i282.UpdateAdminRuleGroupChatDetailUseCase(
            gh<_i170.GroupChatDetailRepository>()));
    gh.factory<_i283.GetUserRulesGroupChatDetailUseCase>(() =>
        _i283.GetUserRulesGroupChatDetailUseCase(
            gh<_i170.GroupChatDetailRepository>()));
    gh.factory<_i284.FileLoadGroupChatDetailUseCase>(() =>
        _i284.FileLoadGroupChatDetailUseCase(
            gh<_i170.GroupChatDetailRepository>()));
    gh.factory<_i285.GetRuleByRoleGroupChatDetailUseCase>(() =>
        _i285.GetRuleByRoleGroupChatDetailUseCase(
            gh<_i170.GroupChatDetailRepository>()));
    gh.factory<_i286.UpdateGroupChatDetailUseCase>(() =>
        _i286.UpdateGroupChatDetailUseCase(
            gh<_i170.GroupChatDetailRepository>()));
    gh.factory<_i287.GetSavedGroupChatDetailUseCase>(() =>
        _i287.GetSavedGroupChatDetailUseCase(
            gh<_i170.GroupChatDetailRepository>()));
    gh.factory<_i288.SaveGroupChatDetailUseCase>(() =>
        _i288.SaveGroupChatDetailUseCase(
            gh<_i170.GroupChatDetailRepository>()));
    gh.lazySingleton<_i289.CustomerRepository>(
        () => _i290.CustomerRepositoryImpl(
              gh<_i9.CustomerApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.factory<_i291.CreateTreatmentOMDetailUseCase>(() =>
        _i291.CreateTreatmentOMDetailUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i292.DeleteResultOfFitUseCase>(() =>
        _i292.DeleteResultOfFitUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i293.GetServiceInsideTicketUseCase>(() =>
        _i293.GetServiceInsideTicketUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i294.GetConsultationNDTVUseCase>(() =>
        _i294.GetConsultationNDTVUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i295.GetResultListOfFitUseCase>(() =>
        _i295.GetResultListOfFitUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i296.GetSkinCustomerInfoUseCase>(() =>
        _i296.GetSkinCustomerInfoUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i297.RemoveConsultationCustomerUseCase>(() =>
        _i297.RemoveConsultationCustomerUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i298.ProductLoadConsultationCustomerUseCase>(() =>
        _i298.ProductLoadConsultationCustomerUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i299.CreateTreatmentDetailUseCase>(() =>
        _i299.CreateTreatmentDetailUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i300.GetFitCustomerInfoUseCase>(() =>
        _i300.GetFitCustomerInfoUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i301.GetTreatmentNoteUseCase>(() =>
        _i301.GetTreatmentNoteUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i302.GetServiceUsageConsultationCustomerUseCase>(() =>
        _i302.GetServiceUsageConsultationCustomerUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i303.GetServiceConsultationCustomerUseCase>(() =>
        _i303.GetServiceConsultationCustomerUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i304.GetTreatmentOMDetailUseCase>(() =>
        _i304.GetTreatmentOMDetailUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i305.GetResultOfFitUseCase>(() => _i305.GetResultOfFitUseCase(
        gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i306.GetSavedConsultationCustomerUseCase>(() =>
        _i306.GetSavedConsultationCustomerUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i307.UpdateSkinCustomerInfoUseCase>(() =>
        _i307.UpdateSkinCustomerInfoUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i308.GetConsultationCustomerUseCase>(() =>
        _i308.GetConsultationCustomerUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i309.UpdateFitCustomerInfoUseCase>(() =>
        _i309.UpdateFitCustomerInfoUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i310.SaveConsultationCustomerUseCase>(() =>
        _i310.SaveConsultationCustomerUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i311.UpdateTreatmentNoteUseCase>(() =>
        _i311.UpdateTreatmentNoteUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i312.GetActionConsultationCustomerUseCase>(() =>
        _i312.GetActionConsultationCustomerUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i313.UpdateResultOfFitUseCase>(() =>
        _i313.UpdateResultOfFitUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i314.UpdateConsultationTTBDUseCase>(() =>
        _i314.UpdateConsultationTTBDUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i315.CompleteConsultationCustomerUseCase>(() =>
        _i315.CompleteConsultationCustomerUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i316.EditServiceConsultationCustomerUseCase>(() =>
        _i316.EditServiceConsultationCustomerUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i317.UpdateTreatmentDetailUseCase>(() =>
        _i317.UpdateTreatmentDetailUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i318.RemoveServiceConsultationCustomerUseCase>(() =>
        _i318.RemoveServiceConsultationCustomerUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i319.GetTreatmentDetailUseCase>(() =>
        _i319.GetTreatmentDetailUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i320.OrderFoodUploadUseCase>(
        () => _i320.OrderFoodUploadUseCase(gh<_i195.FoodRepository>()));
    gh.lazySingleton<_i321.CustomerRecordRepository>(
        () => _i322.CustomerRecordRepositoryImpl(
              gh<_i18.CustomerRecordApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i323.RemoveCreateChatFolderUseCase>(() =>
        _i323.RemoveCreateChatFolderUseCase(
            gh<_i124.CreateChatFolderRepository>()));
    gh.factory<_i324.GetSavedCreateChatFolderUseCase>(() =>
        _i324.GetSavedCreateChatFolderUseCase(
            gh<_i124.CreateChatFolderRepository>()));
    gh.factory<_i325.LoadCreateChatFolderUseCase>(() =>
        _i325.LoadCreateChatFolderUseCase(
            gh<_i124.CreateChatFolderRepository>()));
    gh.factory<_i326.GetCreateChatFolderUseCase>(() =>
        _i326.GetCreateChatFolderUseCase(
            gh<_i124.CreateChatFolderRepository>()));
    gh.factory<_i327.SaveCreateChatFolderUseCase>(() =>
        _i327.SaveCreateChatFolderUseCase(
            gh<_i124.CreateChatFolderRepository>()));
    gh.factory<_i328.ConversationLoadCreateChatFolderUseCase>(() =>
        _i328.ConversationLoadCreateChatFolderUseCase(
            gh<_i124.CreateChatFolderRepository>()));
    gh.factory<_i329.UpdateCreateChatFolderUseCase>(() =>
        _i329.UpdateCreateChatFolderUseCase(
            gh<_i124.CreateChatFolderRepository>()));
    gh.factory<_i330.RemoveFolderCreateChatFolderUseCase>(() =>
        _i330.RemoveFolderCreateChatFolderUseCase(
            gh<_i124.CreateChatFolderRepository>()));
    gh.lazySingleton<_i331.ChatSelectBranchRepository>(
        () => _i332.ChatSelectBranchRepositoryImpl(
              gh<_i18.ChatSelectBranchApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i333.UploadStickerSetUseCase>(() =>
        _i333.UploadStickerSetUseCase(gh<_i183.StickerSocailRepository>()));
    gh.factory<_i334.GetStickerOnlySetUseCase>(() =>
        _i334.GetStickerOnlySetUseCase(gh<_i183.StickerSocailRepository>()));
    gh.factory<_i335.GetStickerRecentUseCase>(() =>
        _i335.GetStickerRecentUseCase(gh<_i183.StickerSocailRepository>()));
    gh.factory<_i336.RemoveStickerUseCase>(
        () => _i336.RemoveStickerUseCase(gh<_i183.StickerSocailRepository>()));
    gh.factory<_i337.UpdateStickerRecentUseCase>(() =>
        _i337.UpdateStickerRecentUseCase(gh<_i183.StickerSocailRepository>()));
    gh.factory<_i338.GetStickerSetUseCase>(
        () => _i338.GetStickerSetUseCase(gh<_i183.StickerSocailRepository>()));
    gh.factory<_i339.CreateStickerListUseCase>(() =>
        _i339.CreateStickerListUseCase(gh<_i183.StickerSocailRepository>()));
    gh.factory<_i340.UploadStickerUseCase>(
        () => _i340.UploadStickerUseCase(gh<_i183.StickerSocailRepository>()));
    gh.factory<_i341.GetStickerListUseCase>(
        () => _i341.GetStickerListUseCase(gh<_i183.StickerSocailRepository>()));
    gh.factory<_i342.RemoveStickerSetUseCase>(() =>
        _i342.RemoveStickerSetUseCase(gh<_i183.StickerSocailRepository>()));
    gh.lazySingleton<_i343.PxListRepository>(() => _i344.PxListRepositoryImpl(
          gh<_i18.PxListApiService>(),
          gh<_i23.EZCache>(),
        ));
    gh.lazySingleton<_i345.TaskRepository>(
        () => _i346.TaskRepositoryImpl(gh<_i9.TaskApiService>()));
    gh.lazySingleton<_i347.DevRepository>(() => _i348.DevRepositoryImpl(
          gh<_i18.DevApiService>(),
          gh<_i23.EZCache>(),
        ));
    gh.lazySingleton<_i349.LocationGoogleRepository>(() =>
        _i350.LocationGoogleRepositoryImpl(
            gh<_i18.LocationGoogleApiService>()));
    gh.factory<_i351.SaveProfileUseCase>(
        () => _i351.SaveProfileUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i352.SaveNavigationUseCase>(
        () => _i352.SaveNavigationUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i353.SaveEnableOnlineLoggerUseCase>(
        () => _i353.SaveEnableOnlineLoggerUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i354.SetOnboardingUseCase>(
        () => _i354.SetOnboardingUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i355.HasUserDataUseCase>(
        () => _i355.HasUserDataUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i356.PersistTokenUseCase>(
        () => _i356.PersistTokenUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i357.DeleteTokenUseCase>(
        () => _i357.DeleteTokenUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i358.ClearCacheUseCase>(
        () => _i358.ClearCacheUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i359.DeleteProfileUseCase>(
        () => _i359.DeleteProfileUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i360.SaveAccountUseCase>(
        () => _i360.SaveAccountUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i361.DeleteHomeMenuCacheUseCase>(
        () => _i361.DeleteHomeMenuCacheUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i362.CheckinUseCase>(
        () => _i362.CheckinUseCase(gh<_i265.UserRepository>()));
    gh.lazySingleton<_i363.MedicalServiceListRepository>(
        () => _i364.MedicalServiceListRepositoryImpl(
              gh<_i18.MedicalServiceListApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.lazySingleton<_i365.ProductConfirmRepository>(() =>
        _i366.ProductConfirmRepositoryImpl(
            gh<_i18.ProductConfirmApiService>()));
    gh.factory<_i367.UniversalQrScanUseCase>(
        () => _i367.UniversalQrScanUseCase(gh<_i195.FoodRepository>()));
    gh.lazySingleton<_i368.DetailCrmCustomerRepository>(
        () => _i369.DetailCrmCustomerRepositoryImpl(
              gh<_i18.DetailCrmCustomerApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i370.TakingCareCustomerRepository>(
        () => _i371.TakingCareCustomerRepositoryImpl(
              gh<_i18.TakingCareCustomerApiService>(),
              gh<_i23.EZCache>(),
              gh<_i142.MediaUploadApiService>(),
            ));
    gh.factory<_i372.GetSavedMedicalDepartmentListUseCase>(() =>
        _i372.GetSavedMedicalDepartmentListUseCase(
            gh<_i201.MedicalDepartmentListRepository>()));
    gh.factory<_i373.GetMedicalDepartmentListUseCase>(() =>
        _i373.GetMedicalDepartmentListUseCase(
            gh<_i201.MedicalDepartmentListRepository>()));
    gh.factory<_i374.SaveMedicalDepartmentListUseCase>(() =>
        _i374.SaveMedicalDepartmentListUseCase(
            gh<_i201.MedicalDepartmentListRepository>()));
    gh.factory<_i375.RemoveMedicalDepartmentListUseCase>(() =>
        _i375.RemoveMedicalDepartmentListUseCase(
            gh<_i201.MedicalDepartmentListRepository>()));
    gh.lazySingleton<_i376.MediaUploadRepository>(() =>
        _i377.MediaUploadRepositoryImpl(gh<_i142.MediaUploadApiService>()));
    gh.lazySingleton<_i378.CustomerBookingInfoRepository>(
        () => _i379.CustomerBookingInfoRepositoryImpl(
              gh<_i18.CustomerBookingInfoApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i380.CustomerListRepository>(
        () => _i381.CustomerListRepositoryImpl(
              gh<_i18.CustomerListApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i382.PxRecheckRepository>(
        () => _i383.PxRecheckRepositoryImpl(
              gh<_i18.PxRecheckApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i384.TicketDetailRepository>(
        () => _i385.TicketDetailRepositoryImpl(
              gh<_i18.TicketDetailApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i386.PostLikeStoryUseCase>(
        () => _i386.PostLikeStoryUseCase(gh<_i193.LikeListRepository>()));
    gh.factory<_i387.PostLikeCommentUseCase>(
        () => _i387.PostLikeCommentUseCase(gh<_i193.LikeListRepository>()));
    gh.factory<_i388.GetLikeListUseCase>(
        () => _i388.GetLikeListUseCase(gh<_i193.LikeListRepository>()));
    gh.lazySingleton<_i389.NewsRepository>(
        () => _i390.NewsRepositoryImpl(gh<_i9.NewsApiService>()));
    gh.lazySingleton<_i391.CreateCustomerRepository>(
        () => _i392.CreateCustomerRepositoryImpl(
              gh<_i18.CreateCustomerApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i393.CheckinRepository>(
        () => _i394.CheckinRepositoryImpl(gh<_i9.CheckinApiService>()));
    gh.factory<_i395.CreatingTaskUseCase>(
        () => _i395.CreatingTaskUseCase(gh<_i345.TaskRepository>()));
    gh.factory<_i396.GetRepeatTaskUseCase>(
        () => _i396.GetRepeatTaskUseCase(gh<_i345.TaskRepository>()));
    gh.factory<_i397.GetJobSchedulerUseCase>(
        () => _i397.GetJobSchedulerUseCase(gh<_i345.TaskRepository>()));
    gh.factory<_i398.SubmitJobSchedulerUseCase>(
        () => _i398.SubmitJobSchedulerUseCase(gh<_i345.TaskRepository>()));
    gh.factory<_i399.GetGeneralJobSchedulerUseCase>(
        () => _i399.GetGeneralJobSchedulerUseCase(gh<_i345.TaskRepository>()));
    gh.factory<_i400.GetDetailJobSchedulerUseCase>(
        () => _i400.GetDetailJobSchedulerUseCase(gh<_i345.TaskRepository>()));
    gh.lazySingleton<_i401.CustomerProfileRepository>(
        () => _i402.CustomerProfileRepositoryImpl(
              gh<_i9.CustomerProfileApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.factory<_i403.GetStoryPersonListUseCase>(() =>
        _i403.GetStoryPersonListUseCase(gh<_i166.StoryPersonListRepository>()));
    gh.factory<_i404.GetStoryPersonListUserUseCase>(() =>
        _i404.GetStoryPersonListUserUseCase(
            gh<_i166.StoryPersonListRepository>()));
    gh.factory<_i405.SaveFontOptionUseCase>(
        () => _i405.SaveFontOptionUseCase(gh<_i140.SettingRepository>()));
    gh.factory<_i406.GetLanguageOptionUseCase>(
        () => _i406.GetLanguageOptionUseCase(gh<_i140.SettingRepository>()));
    gh.factory<_i407.GetThemeOptionUseCase>(
        () => _i407.GetThemeOptionUseCase(gh<_i140.SettingRepository>()));
    gh.factory<_i408.SaveLanguageOptionUseCase>(
        () => _i408.SaveLanguageOptionUseCase(gh<_i140.SettingRepository>()));
    gh.factory<_i409.SaveThemeOptionUseCase>(
        () => _i409.SaveThemeOptionUseCase(gh<_i140.SettingRepository>()));
    gh.factory<_i410.IsDarkModeUseCase>(
        () => _i410.IsDarkModeUseCase(gh<_i140.SettingRepository>()));
    gh.factory<_i411.GetFontOptionUseCase>(
        () => _i411.GetFontOptionUseCase(gh<_i140.SettingRepository>()));
    gh.factory<_i412.IsLightModeUseCase>(
        () => _i412.IsLightModeUseCase(gh<_i140.SettingRepository>()));
    gh.lazySingleton<_i413.NoteDetailsRepository>(
        () => _i414.NoteDetailsRepositoryImpl(
              gh<_i18.NoteDetailsApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.factory<_i415.RepeatTaskBloc>(
        () => _i415.RepeatTaskBloc(gh<_i396.GetRepeatTaskUseCase>()));
    gh.lazySingleton<_i416.BranchChatListRepository>(
        () => _i417.BranchChatListRepositoryImpl(
              gh<_i18.BranchChatListApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i418.GetSelectPxRoomUseCase>(
        () => _i418.GetSelectPxRoomUseCase(gh<_i191.SelectPxRoomRepository>()));
    gh.factory<_i419.RemoveSelectPxRoomUseCase>(() =>
        _i419.RemoveSelectPxRoomUseCase(gh<_i191.SelectPxRoomRepository>()));
    gh.factory<_i420.RoomChangeSelectPxRoomUseCase>(() =>
        _i420.RoomChangeSelectPxRoomUseCase(
            gh<_i191.SelectPxRoomRepository>()));
    gh.factory<_i421.GetSavedSelectPxRoomUseCase>(() =>
        _i421.GetSavedSelectPxRoomUseCase(gh<_i191.SelectPxRoomRepository>()));
    gh.factory<_i422.SaveSelectPxRoomUseCase>(() =>
        _i422.SaveSelectPxRoomUseCase(gh<_i191.SelectPxRoomRepository>()));
    gh.lazySingleton<_i423.ListCustomerRepository>(
        () => _i424.ListCustomerRepositoryImpl(
              gh<_i9.ListCustomerApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.factory<_i425.ChatBloc>(() => _i425.ChatBloc(
          gh<_i229.GetChatUseCase>(),
          gh<_i218.SaveChatUseCase>(),
          gh<_i211.GetSavedChatUseCase>(),
          gh<_i231.RemoveChatUseCase>(),
          gh<_i226.SendChatUseCase>(),
          gh<_i221.UploadFileChatUseCase>(),
          gh<_i232.ConversationDetailsUpdateChatUseCase>(),
          gh<_i224.ReactChatUseCase>(),
          gh<_i225.GetConversationByIdUseCase>(),
          gh<_i230.MessageRemoveChatUseCase>(),
          gh<_i212.MessageEditChatUseCase>(),
          gh<_i216.GetConversationChatUseCase>(),
          gh<_i227.PinMessageChatUseCase>(),
          gh<_i222.UnpinMessageChatUseCase>(),
          gh<_i217.GetPinListChatUseCase>(),
          gh<_i228.UpdatePollChatUseCase>(),
          gh<_i219.VotePollChatUseCase>(),
          gh<_i213.GetUserSeenChatUseCase>(),
          gh<_i215.SearchChatUseCase>(),
          gh<_i214.TranscribeChatUseCase>(),
          gh<_i223.ReplyBotMessageChatUseCase>(),
          gh<_i220.GetUserStickerChatUseCase>(),
          gh<_i283.GetUserRulesGroupChatDetailUseCase>(),
        ));
    gh.lazySingleton<_i426.ScheduleDetailsRepository>(
        () => _i427.ScheduleDetailsRepositoryImpl(
              gh<_i428.ScheduleDetailsApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.factory<_i429.TicketCreatedTypeUseCase>(
        () => _i429.TicketCreatedTypeUseCase(gh<_i255.TicketRepository>()));
    gh.factory<_i430.GetTicketAllGroupUseCase>(
        () => _i430.GetTicketAllGroupUseCase(gh<_i255.TicketRepository>()));
    gh.factory<_i431.CreateTicketUseCase>(
        () => _i431.CreateTicketUseCase(gh<_i255.TicketRepository>()));
    gh.factory<_i432.GetTicketTicketv2UseCase>(
        () => _i432.GetTicketTicketv2UseCase(gh<_i255.TicketRepository>()));
    gh.factory<_i433.TicketUploadFileUseCase>(
        () => _i433.TicketUploadFileUseCase(gh<_i255.TicketRepository>()));
    gh.factory<_i434.GetMyTicketTicketv2UseCase>(
        () => _i434.GetMyTicketTicketv2UseCase(gh<_i255.TicketRepository>()));
    gh.factory<_i435.UpdateTicketUseCase>(
        () => _i435.UpdateTicketUseCase(gh<_i255.TicketRepository>()));
    gh.factory<_i436.GetTicketAllTypeUseCase>(
        () => _i436.GetTicketAllTypeUseCase(gh<_i255.TicketRepository>()));
    gh.factory<_i437.TicketGroupTypeUseCase>(
        () => _i437.TicketGroupTypeUseCase(gh<_i255.TicketRepository>()));
    gh.lazySingleton<_i438.StaffEvaluationPeriodsRepository>(
        () => _i439.StaffEvaluationPeriodsRepositoryImpl(
              gh<_i18.StaffEvaluationPeriodsApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i440.PxUnasignedRepository>(
        () => _i441.PxUnasignedRepositoryImpl(
              gh<_i18.PxUnasignedApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i442.GetDropDownStatusUseCase>(
        () => _i442.GetDropDownStatusUseCase(gh<_i207.UserTickerRepository>()));
    gh.factory<_i443.GetUserTicketUseCase>(
        () => _i443.GetUserTicketUseCase(gh<_i207.UserTickerRepository>()));
    gh.lazySingleton<_i444.StaffRepository>(
        () => _i445.StaffRepositoryImpl(gh<_i9.StaffApiService>()));
    gh.factory<_i446.RemoveCustomerRecordUseCase>(() =>
        _i446.RemoveCustomerRecordUseCase(
            gh<_i321.CustomerRecordRepository>()));
    gh.factory<_i447.GetSavedCustomerRecordUseCase>(() =>
        _i447.GetSavedCustomerRecordUseCase(
            gh<_i321.CustomerRecordRepository>()));
    gh.factory<_i448.GetCustomerRecordUseCase>(() =>
        _i448.GetCustomerRecordUseCase(gh<_i321.CustomerRecordRepository>()));
    gh.factory<_i449.SaveCustomerRecordUseCase>(() =>
        _i449.SaveCustomerRecordUseCase(gh<_i321.CustomerRecordRepository>()));
    gh.lazySingleton<_i450.MedicalProductCreationRepository>(
        () => _i451.MedicalProductCreationRepositoryImpl(
              gh<_i18.MedicalProductCreationApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.lazySingleton<_i452.HomeRepository>(
        () => _i453.HomeRepositoryImpl(gh<_i9.HomeApiService>()));
    gh.factory<_i454.GetEmojiListUseCase>(
        () => _i454.GetEmojiListUseCase(gh<_i168.StoryListRepository>()));
    gh.factory<_i455.GetStoryListUseCase>(
        () => _i455.GetStoryListUseCase(gh<_i168.StoryListRepository>()));
    gh.factory<_i456.GetStoryListSearchUseCase>(
        () => _i456.GetStoryListSearchUseCase(gh<_i168.StoryListRepository>()));
    gh.factory<_i457.PostStoryUseCase>(
        () => _i457.PostStoryUseCase(gh<_i168.StoryListRepository>()));
    gh.factory<_i458.PutStoryVoteUseCase>(
        () => _i458.PutStoryVoteUseCase(gh<_i168.StoryListRepository>()));
    gh.factory<_i459.GetTotalNotificationUseCase>(() =>
        _i459.GetTotalNotificationUseCase(gh<_i168.StoryListRepository>()));
    gh.factory<_i460.UpdateStoryUseCase>(
        () => _i460.UpdateStoryUseCase(gh<_i168.StoryListRepository>()));
    gh.factory<_i461.SocialUploadFileUseCase>(
        () => _i461.SocialUploadFileUseCase(gh<_i168.StoryListRepository>()));
    gh.factory<_i462.GetVoteUsersUseCase>(
        () => _i462.GetVoteUsersUseCase(gh<_i168.StoryListRepository>()));
    gh.factory<_i463.DeleteStoryVoteUseCase>(
        () => _i463.DeleteStoryVoteUseCase(gh<_i168.StoryListRepository>()));
    gh.factory<_i464.DeleteStoryUseCase>(
        () => _i464.DeleteStoryUseCase(gh<_i168.StoryListRepository>()));
    gh.factory<_i465.GetStoryRuleUseCase>(
        () => _i465.GetStoryRuleUseCase(gh<_i168.StoryListRepository>()));
    gh.lazySingleton<_i466.CommentListRepository>(
        () => _i467.CommentListRepositoryImpl(
              gh<_i15.CommentListApiService>(),
              gh<_i144.SocialApiService>(),
            ));
    gh.factory<_i468.GetSavedMedicalServiceListUseCase>(() =>
        _i468.GetSavedMedicalServiceListUseCase(
            gh<_i363.MedicalServiceListRepository>()));
    gh.factory<_i469.SaveMedicalServiceListUseCase>(() =>
        _i469.SaveMedicalServiceListUseCase(
            gh<_i363.MedicalServiceListRepository>()));
    gh.factory<_i470.RemoveMedicalServiceListUseCase>(() =>
        _i470.RemoveMedicalServiceListUseCase(
            gh<_i363.MedicalServiceListRepository>()));
    gh.factory<_i471.GetMedicalServiceListUseCase>(() =>
        _i471.GetMedicalServiceListUseCase(
            gh<_i363.MedicalServiceListRepository>()));
    gh.factory<_i472.CreateChatGroupBloc>(() => _i472.CreateChatGroupBloc(
          gh<_i181.GetCreateChatGroupUseCase>(),
          gh<_i179.SaveCreateChatGroupUseCase>(),
          gh<_i178.GetSavedCreateChatGroupUseCase>(),
          gh<_i182.RemoveCreateChatGroupUseCase>(),
          gh<_i180.UserLoadCreateChatGroupUseCase>(),
          gh<_i221.UploadFileChatUseCase>(),
        ));
    gh.factory<_i473.GetSavedChatSelectBranchUseCase>(() =>
        _i473.GetSavedChatSelectBranchUseCase(
            gh<_i331.ChatSelectBranchRepository>()));
    gh.factory<_i474.RemoveChatSelectBranchUseCase>(() =>
        _i474.RemoveChatSelectBranchUseCase(
            gh<_i331.ChatSelectBranchRepository>()));
    gh.factory<_i475.SaveChatSelectBranchUseCase>(() =>
        _i475.SaveChatSelectBranchUseCase(
            gh<_i331.ChatSelectBranchRepository>()));
    gh.factory<_i476.GetChatSelectBranchUseCase>(() =>
        _i476.GetChatSelectBranchUseCase(
            gh<_i331.ChatSelectBranchRepository>()));
    gh.factory<_i477.SaveDetailStaffEvaluationPeriodUseCase>(() =>
        _i477.SaveDetailStaffEvaluationPeriodUseCase(
            gh<_i203.DetailStaffEvaluationPeriodRepository>()));
    gh.factory<_i478.EmployeeFetchDetailStaffEvaluationPeriodUseCase>(() =>
        _i478.EmployeeFetchDetailStaffEvaluationPeriodUseCase(
            gh<_i203.DetailStaffEvaluationPeriodRepository>()));
    gh.factory<_i479.RemoveDetailStaffEvaluationPeriodUseCase>(() =>
        _i479.RemoveDetailStaffEvaluationPeriodUseCase(
            gh<_i203.DetailStaffEvaluationPeriodRepository>()));
    gh.factory<_i480.GetSavedDetailStaffEvaluationPeriodUseCase>(() =>
        _i480.GetSavedDetailStaffEvaluationPeriodUseCase(
            gh<_i203.DetailStaffEvaluationPeriodRepository>()));
    gh.factory<_i481.GetDetailStaffEvaluationPeriodUseCase>(() =>
        _i481.GetDetailStaffEvaluationPeriodUseCase(
            gh<_i203.DetailStaffEvaluationPeriodRepository>()));
    gh.factory<_i482.FetchHistoryCheckinUseCase>(
        () => _i482.FetchHistoryCheckinUseCase(gh<_i393.CheckinRepository>()));
    gh.lazySingleton<_i483.NotificationRepository>(() =>
        _i484.NotificationRepositoryImpl(gh<_i9.NotificationApiService>()));
    gh.lazySingleton<_i485.EformRepository>(
        () => _i486.EformRepositoryImpl(gh<_i9.EformApiService>()));
    gh.factory<_i487.MedicalServiceListBloc>(() => _i487.MedicalServiceListBloc(
          gh<_i471.GetMedicalServiceListUseCase>(),
          gh<_i469.SaveMedicalServiceListUseCase>(),
          gh<_i468.GetSavedMedicalServiceListUseCase>(),
          gh<_i470.RemoveMedicalServiceListUseCase>(),
        ));
    gh.factory<_i488.DetailJobSchedulerBloc>(() => _i488.DetailJobSchedulerBloc(
          gh<_i400.GetDetailJobSchedulerUseCase>(),
          gh<_i398.SubmitJobSchedulerUseCase>(),
        ));
    gh.factory<_i489.FontsBloc>(() => _i489.FontsBloc(
          gh<_i411.GetFontOptionUseCase>(),
          gh<_i405.SaveFontOptionUseCase>(),
          gh<_i243.CacheTextScaleGetUseCase>(),
          gh<_i234.CacheTextScaleSaveUseCase>(),
        ));
    gh.factory<_i490.StoryPersonListBloc>(() => _i490.StoryPersonListBloc(
          gh<_i403.GetStoryPersonListUseCase>(),
          gh<_i464.DeleteStoryUseCase>(),
          gh<_i386.PostLikeStoryUseCase>(),
          gh<_i457.PostStoryUseCase>(),
          gh<_i460.UpdateStoryUseCase>(),
          gh<_i404.GetStoryPersonListUserUseCase>(),
        ));
    gh.factory<_i491.NotificationListBloc>(() => _i491.NotificationListBloc(
          gh<_i260.GetNotificationListUseCase>(),
          gh<_i263.SaveNotificationListUseCase>(),
          gh<_i264.GetSavedNotificationListUseCase>(),
          gh<_i259.RemoveNotificationListUseCase>(),
          gh<_i261.PutReadAllSocialUseCase>(),
          gh<_i262.DeleteNotificationSocialUseCase>(),
        ));
    gh.factory<_i492.GetSavedImportantNotesUseCase>(() =>
        _i492.GetSavedImportantNotesUseCase(
            gh<_i205.ImportantNotesRepository>()));
    gh.factory<_i493.SaveImportantNotesUseCase>(() =>
        _i493.SaveImportantNotesUseCase(gh<_i205.ImportantNotesRepository>()));
    gh.factory<_i494.GetNoteCategoryImportantNotesUseCase>(() =>
        _i494.GetNoteCategoryImportantNotesUseCase(
            gh<_i205.ImportantNotesRepository>()));
    gh.factory<_i495.GetImportantNotesUseCase>(() =>
        _i495.GetImportantNotesUseCase(gh<_i205.ImportantNotesRepository>()));
    gh.factory<_i496.RemoveImportantNotesUseCase>(() =>
        _i496.RemoveImportantNotesUseCase(
            gh<_i205.ImportantNotesRepository>()));
    gh.lazySingleton<_i497.ServiceAndProductRepository>(
        () => _i498.ServiceAndProductRepositoryImpl(
              gh<_i18.ServiceAndProductApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.factory<_i499.RemoveStaffEvaluationPeriodsUseCase>(() =>
        _i499.RemoveStaffEvaluationPeriodsUseCase(
            gh<_i438.StaffEvaluationPeriodsRepository>()));
    gh.factory<_i500.SaveStaffEvaluationPeriodsUseCase>(() =>
        _i500.SaveStaffEvaluationPeriodsUseCase(
            gh<_i438.StaffEvaluationPeriodsRepository>()));
    gh.factory<_i501.GetSavedStaffEvaluationPeriodsUseCase>(() =>
        _i501.GetSavedStaffEvaluationPeriodsUseCase(
            gh<_i438.StaffEvaluationPeriodsRepository>()));
    gh.factory<_i502.GetStaffEvaluationPeriodsUseCase>(() =>
        _i502.GetStaffEvaluationPeriodsUseCase(
            gh<_i438.StaffEvaluationPeriodsRepository>()));
    gh.factory<_i503.GetCustomerInfoByQrUseCase>(
        () => _i503.GetCustomerInfoByQrUseCase(gh<_i289.CustomerRepository>()));
    gh.factory<_i504.GetCustomerInfoUseCase>(
        () => _i504.GetCustomerInfoUseCase(gh<_i289.CustomerRepository>()));
    gh.factory<_i505.GetCustomerRoomCodeUseCase>(
        () => _i505.GetCustomerRoomCodeUseCase(gh<_i289.CustomerRepository>()));
    gh.lazySingleton<_i506.MedicalLogDetailRepository>(
        () => _i507.MedicalLogDetailRepositoryImpl(
              gh<_i18.MedicalLogDetailApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.factory<_i508.ProductsMedicalProductCreationUseCase>(() =>
        _i508.ProductsMedicalProductCreationUseCase(
            gh<_i450.MedicalProductCreationRepository>()));
    gh.factory<_i509.GetSavedMedicalProductCreationUseCase>(() =>
        _i509.GetSavedMedicalProductCreationUseCase(
            gh<_i450.MedicalProductCreationRepository>()));
    gh.factory<_i510.RemoveMedicalProductCreationUseCase>(() =>
        _i510.RemoveMedicalProductCreationUseCase(
            gh<_i450.MedicalProductCreationRepository>()));
    gh.factory<_i511.SaveMedicalProductCreationUseCase>(() =>
        _i511.SaveMedicalProductCreationUseCase(
            gh<_i450.MedicalProductCreationRepository>()));
    gh.factory<_i512.MedicalProductCreationUseCase>(() =>
        _i512.MedicalProductCreationUseCase(
            gh<_i450.MedicalProductCreationRepository>()));
    gh.lazySingleton<_i513.CustomerScheduleRepository>(
        () => _i514.CustomerScheduleRepositoryImpl(
              gh<_i18.CustomerScheduleApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.lazySingleton<_i515.KpiEmployeeRepository>(() =>
        _i516.KpiEmployeeRepositoryImpl(gh<_i18.KpiEmployeeApiService>()));
    gh.factory<_i517.RemovePxTaskListUseCase>(
        () => _i517.RemovePxTaskListUseCase(gh<_i253.PxTaskListRepository>()));
    gh.factory<_i518.GetSavedPxTaskListUseCase>(() =>
        _i518.GetSavedPxTaskListUseCase(gh<_i253.PxTaskListRepository>()));
    gh.factory<_i519.SavePxTaskListUseCase>(
        () => _i519.SavePxTaskListUseCase(gh<_i253.PxTaskListRepository>()));
    gh.factory<_i520.GetPxTaskListUseCase>(
        () => _i520.GetPxTaskListUseCase(gh<_i253.PxTaskListRepository>()));
    gh.factory<_i521.ChatSelectBranchBloc>(() => _i521.ChatSelectBranchBloc(
          gh<_i476.GetChatSelectBranchUseCase>(),
          gh<_i475.SaveChatSelectBranchUseCase>(),
          gh<_i474.RemoveChatSelectBranchUseCase>(),
        ));
    gh.factory<_i522.OrderFoodDeleteUseCase>(
        () => _i522.OrderFoodDeleteUseCase(gh<_i195.FoodRepository>()));
    gh.factory<_i523.OrderFoodGetUseCase>(
        () => _i523.OrderFoodGetUseCase(gh<_i195.FoodRepository>()));
    gh.factory<_i524.OrderFoodCreatedUseCase>(
        () => _i524.OrderFoodCreatedUseCase(gh<_i195.FoodRepository>()));
    gh.factory<_i525.OrderFoodCreateReportUseCase>(
        () => _i525.OrderFoodCreateReportUseCase(gh<_i195.FoodRepository>()));
    gh.factory<_i526.SetDefaultAddressFoodUseCase>(
        () => _i526.SetDefaultAddressFoodUseCase(gh<_i195.FoodRepository>()));
    gh.factory<_i527.RoomLoadDetailCrmCustomerUseCase>(() =>
        _i527.RoomLoadDetailCrmCustomerUseCase(
            gh<_i368.DetailCrmCustomerRepository>()));
    gh.factory<_i528.PromotionLoadDetailCrmCustomerUseCase>(() =>
        _i528.PromotionLoadDetailCrmCustomerUseCase(
            gh<_i368.DetailCrmCustomerRepository>()));
    gh.factory<_i529.ServiceFetchDetailCrmCustomerUseCase>(() =>
        _i529.ServiceFetchDetailCrmCustomerUseCase(
            gh<_i368.DetailCrmCustomerRepository>()));
    gh.factory<_i530.AdviceFetchDetailCrmCustomerUseCase>(() =>
        _i530.AdviceFetchDetailCrmCustomerUseCase(
            gh<_i368.DetailCrmCustomerRepository>()));
    gh.factory<_i531.GetDetailCrmCustomerUseCase>(() =>
        _i531.GetDetailCrmCustomerUseCase(
            gh<_i368.DetailCrmCustomerRepository>()));
    gh.factory<_i532.SaveDetailCrmCustomerUseCase>(() =>
        _i532.SaveDetailCrmCustomerUseCase(
            gh<_i368.DetailCrmCustomerRepository>()));
    gh.factory<_i533.ServiceLoadDetailCrmCustomerUseCase>(() =>
        _i533.ServiceLoadDetailCrmCustomerUseCase(
            gh<_i368.DetailCrmCustomerRepository>()));
    gh.factory<_i534.RemoveDetailCrmCustomerUseCase>(() =>
        _i534.RemoveDetailCrmCustomerUseCase(
            gh<_i368.DetailCrmCustomerRepository>()));
    gh.factory<_i535.CallLogFetchDetailCrmCustomerUseCase>(() =>
        _i535.CallLogFetchDetailCrmCustomerUseCase(
            gh<_i368.DetailCrmCustomerRepository>()));
    gh.factory<_i536.BranchLoadDetailCrmCustomerUseCase>(() =>
        _i536.BranchLoadDetailCrmCustomerUseCase(
            gh<_i368.DetailCrmCustomerRepository>()));
    gh.factory<_i537.GetSavedDetailCrmCustomerUseCase>(() =>
        _i537.GetSavedDetailCrmCustomerUseCase(
            gh<_i368.DetailCrmCustomerRepository>()));
    gh.factory<_i538.BookingLogFetchDetailCrmCustomerUseCase>(() =>
        _i538.BookingLogFetchDetailCrmCustomerUseCase(
            gh<_i368.DetailCrmCustomerRepository>()));
    gh.factory<_i539.BookDetailCrmCustomerUseCase>(() =>
        _i539.BookDetailCrmCustomerUseCase(
            gh<_i368.DetailCrmCustomerRepository>()));
    gh.factory<_i540.TimeLoadDetailCrmCustomerUseCase>(() =>
        _i540.TimeLoadDetailCrmCustomerUseCase(
            gh<_i368.DetailCrmCustomerRepository>()));
    gh.factory<_i541.AdviceUpdateDetailCrmCustomerUseCase>(() =>
        _i541.AdviceUpdateDetailCrmCustomerUseCase(
            gh<_i368.DetailCrmCustomerRepository>()));
    gh.factory<_i542.AdviceTypeFetchDetailCrmCustomerUseCase>(() =>
        _i542.AdviceTypeFetchDetailCrmCustomerUseCase(
            gh<_i368.DetailCrmCustomerRepository>()));
    gh.factory<_i543.BookingDetailLoadDetailCrmCustomerUseCase>(() =>
        _i543.BookingDetailLoadDetailCrmCustomerUseCase(
            gh<_i368.DetailCrmCustomerRepository>()));
    gh.factory<_i544.MessageLogFetchDetailCrmCustomerUseCase>(() =>
        _i544.MessageLogFetchDetailCrmCustomerUseCase(
            gh<_i368.DetailCrmCustomerRepository>()));
    gh.factory<_i545.NumberBookingLoadDetailCrmCustomerUseCase>(() =>
        _i545.NumberBookingLoadDetailCrmCustomerUseCase(
            gh<_i368.DetailCrmCustomerRepository>()));
    gh.factory<_i546.BookingLoadDetailCrmCustomerUseCase>(() =>
        _i546.BookingLoadDetailCrmCustomerUseCase(
            gh<_i368.DetailCrmCustomerRepository>()));
    gh.lazySingleton<_i547.PxUnasignedUpdateRepository>(
        () => _i548.PxUnasignedUpdateRepositoryImpl(
              gh<_i18.PxUnasignedUpdateApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.lazySingleton<_i549.UserListRepository>(
        () => _i550.UserListRepositoryImpl(
              gh<_i18.UserListApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i551.GetServicesUseCase>(
        () => _i551.GetServicesUseCase(gh<_i452.HomeRepository>()));
    gh.factory<_i552.PopupInfoUseCase>(
        () => _i552.PopupInfoUseCase(gh<_i452.HomeRepository>()));
    gh.factory<_i553.CreateChatFolderBloc>(() => _i553.CreateChatFolderBloc(
          gh<_i326.GetCreateChatFolderUseCase>(),
          gh<_i327.SaveCreateChatFolderUseCase>(),
          gh<_i324.GetSavedCreateChatFolderUseCase>(),
          gh<_i323.RemoveCreateChatFolderUseCase>(),
          gh<_i325.LoadCreateChatFolderUseCase>(),
          gh<_i330.RemoveFolderCreateChatFolderUseCase>(),
          gh<_i328.ConversationLoadCreateChatFolderUseCase>(),
          gh<_i329.UpdateCreateChatFolderUseCase>(),
        ));
    gh.factory<_i554.SaveTicketDetailUseCase>(() =>
        _i554.SaveTicketDetailUseCase(gh<_i384.TicketDetailRepository>()));
    gh.factory<_i555.GetTicketDetailUseCase>(
        () => _i555.GetTicketDetailUseCase(gh<_i384.TicketDetailRepository>()));
    gh.factory<_i556.GetTicketDetailReasonUseCase>(() =>
        _i556.GetTicketDetailReasonUseCase(gh<_i384.TicketDetailRepository>()));
    gh.factory<_i557.GetSavedTicketDetailUseCase>(() =>
        _i557.GetSavedTicketDetailUseCase(gh<_i384.TicketDetailRepository>()));
    gh.factory<_i558.ReceptTicketDetailUseCase>(() =>
        _i558.ReceptTicketDetailUseCase(gh<_i384.TicketDetailRepository>()));
    gh.factory<_i559.TicketDetailReworkUseCase>(() =>
        _i559.TicketDetailReworkUseCase(gh<_i384.TicketDetailRepository>()));
    gh.factory<_i560.ConfirmTicketDetailUseCase>(() =>
        _i560.ConfirmTicketDetailUseCase(gh<_i384.TicketDetailRepository>()));
    gh.factory<_i561.RemoveTicketDetailUseCase>(() =>
        _i561.RemoveTicketDetailUseCase(gh<_i384.TicketDetailRepository>()));
    gh.lazySingleton<_i562.CustomerInfoDetailsRepository>(
        () => _i563.CustomerInfoDetailsRepositoryImpl(
              gh<_i9.CustomerInfoDetailsApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.lazySingleton<_i564.HrOrganizationRepository>(
        () => _i565.HrOrganizationRepositoryImpl(
              gh<_i18.HrOrganizationApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i566.RemoveScheduleDetailsUseCase>(() =>
        _i566.RemoveScheduleDetailsUseCase(
            gh<_i426.ScheduleDetailsRepository>()));
    gh.factory<_i567.SaveScheduleDetailsUseCase>(() =>
        _i567.SaveScheduleDetailsUseCase(
            gh<_i426.ScheduleDetailsRepository>()));
    gh.factory<_i568.GetSavedScheduleDetailsUseCase>(() =>
        _i568.GetSavedScheduleDetailsUseCase(
            gh<_i426.ScheduleDetailsRepository>()));
    gh.factory<_i569.GetScheduleDetailsUseCase>(() =>
        _i569.GetScheduleDetailsUseCase(gh<_i426.ScheduleDetailsRepository>()));
    gh.factory<_i570.GetProvinceCreateCustomerUseCase>(() =>
        _i570.GetProvinceCreateCustomerUseCase(
            gh<_i391.CreateCustomerRepository>()));
    gh.factory<_i571.GetJobCreateCustomerUseCase>(() =>
        _i571.GetJobCreateCustomerUseCase(
            gh<_i391.CreateCustomerRepository>()));
    gh.factory<_i572.RemoveCreateCustomerUseCase>(() =>
        _i572.RemoveCreateCustomerUseCase(
            gh<_i391.CreateCustomerRepository>()));
    gh.factory<_i573.SaveCreateCustomerUseCase>(() =>
        _i573.SaveCreateCustomerUseCase(gh<_i391.CreateCustomerRepository>()));
    gh.factory<_i574.GetDistrictCreateCustomerUseCase>(() =>
        _i574.GetDistrictCreateCustomerUseCase(
            gh<_i391.CreateCustomerRepository>()));
    gh.factory<_i575.GetWardCreateCustomerUseCase>(() =>
        _i575.GetWardCreateCustomerUseCase(
            gh<_i391.CreateCustomerRepository>()));
    gh.factory<_i576.GetCreateCustomerUseCase>(() =>
        _i576.GetCreateCustomerUseCase(gh<_i391.CreateCustomerRepository>()));
    gh.factory<_i577.GetSavedCreateCustomerUseCase>(() =>
        _i577.GetSavedCreateCustomerUseCase(
            gh<_i391.CreateCustomerRepository>()));
    gh.factory<_i578.UpdateCreateCustomerUseCase>(() =>
        _i578.UpdateCreateCustomerUseCase(
            gh<_i391.CreateCustomerRepository>()));
    gh.factory<_i579.SurveyLoadCreateCustomerUseCase>(() =>
        _i579.SurveyLoadCreateCustomerUseCase(
            gh<_i391.CreateCustomerRepository>()));
    gh.factory<_i580.CustomerSearchCreateCustomerUseCase>(() =>
        _i580.CustomerSearchCreateCustomerUseCase(
            gh<_i391.CreateCustomerRepository>()));
    gh.factory<_i581.InfoCustomerBloc>(
        () => _i581.InfoCustomerBloc(gh<_i504.GetCustomerInfoUseCase>()));
    gh.factory<_i582.GetProductConfirmBranchUseCase>(() =>
        _i582.GetProductConfirmBranchUseCase(
            gh<_i365.ProductConfirmRepository>()));
    gh.factory<_i583.GetProductDetailConfirmUseCase>(() =>
        _i583.GetProductDetailConfirmUseCase(
            gh<_i365.ProductConfirmRepository>()));
    gh.factory<_i584.ApprovalProductDetailConfirmUsecase>(() =>
        _i584.ApprovalProductDetailConfirmUsecase(
            gh<_i365.ProductConfirmRepository>()));
    gh.factory<_i585.GetProductConfirmUseCase>(() =>
        _i585.GetProductConfirmUseCase(gh<_i365.ProductConfirmRepository>()));
    gh.factory<_i586.RejectProductDetailConfirmUsecase>(() =>
        _i586.RejectProductDetailConfirmUsecase(
            gh<_i365.ProductConfirmRepository>()));
    gh.factory<_i587.SocketAccessTokenGetLoginUseCase>(() =>
        _i587.SocketAccessTokenGetLoginUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i588.GetNewsUseCase>(
        () => _i588.GetNewsUseCase(gh<_i389.NewsRepository>()));
    gh.factory<_i589.SearchNewsUseCase>(
        () => _i589.SearchNewsUseCase(gh<_i389.NewsRepository>()));
    gh.factory<_i590.GetDetailNewsUseCase>(
        () => _i590.GetDetailNewsUseCase(gh<_i389.NewsRepository>()));
    gh.factory<_i591.GetDepartmentUseCase>(
        () => _i591.GetDepartmentUseCase(gh<_i444.StaffRepository>()));
    gh.factory<_i592.GetFunctionRoomUseCase>(
        () => _i592.GetFunctionRoomUseCase(gh<_i444.StaffRepository>()));
    gh.factory<_i593.GetStaffUseCase>(
        () => _i593.GetStaffUseCase(gh<_i444.StaffRepository>()));
    gh.factory<_i594.CreateEformUseCase>(
        () => _i594.CreateEformUseCase(gh<_i485.EformRepository>()));
    gh.factory<_i595.ApprovingSignalEformUseCase>(
        () => _i595.ApprovingSignalEformUseCase(gh<_i485.EformRepository>()));
    gh.factory<_i596.ApprovingOtpEformUseCase>(
        () => _i596.ApprovingOtpEformUseCase(gh<_i485.EformRepository>()));
    gh.factory<_i597.GetEformRequestTypeUseCase>(
        () => _i597.GetEformRequestTypeUseCase(gh<_i485.EformRepository>()));
    gh.factory<_i598.RejectEformUseCase>(
        () => _i598.RejectEformUseCase(gh<_i485.EformRepository>()));
    gh.factory<_i599.ApprovingEformUseCase>(
        () => _i599.ApprovingEformUseCase(gh<_i485.EformRepository>()));
    gh.factory<_i600.GetDetailEformUseCase>(
        () => _i600.GetDetailEformUseCase(gh<_i485.EformRepository>()));
    gh.factory<_i601.GetEformUseCase>(
        () => _i601.GetEformUseCase(gh<_i485.EformRepository>()));
    gh.factory<_i602.NewsBloc>(() => _i602.NewsBloc(
          gh<_i588.GetNewsUseCase>(),
          gh<_i589.SearchNewsUseCase>(),
        ));
    gh.factory<_i603.OrderFoodBloc>(() => _i603.OrderFoodBloc(
          gh<_i523.OrderFoodGetUseCase>(),
          gh<_i524.OrderFoodCreatedUseCase>(),
          gh<_i522.OrderFoodDeleteUseCase>(),
          gh<_i367.UniversalQrScanUseCase>(),
          gh<_i526.SetDefaultAddressFoodUseCase>(),
        ));
    gh.factory<_i604.CompleteTicketActiveUseCase>(() =>
        _i604.CompleteTicketActiveUseCase(gh<_i199.TicketActiveRepository>()));
    gh.factory<_i605.GetTicketActiveUseCase>(
        () => _i605.GetTicketActiveUseCase(gh<_i199.TicketActiveRepository>()));
    gh.factory<_i606.CreateTicketActiveUseCase>(() =>
        _i606.CreateTicketActiveUseCase(gh<_i199.TicketActiveRepository>()));
    gh.factory<_i607.DetailEformBloc>(() => _i607.DetailEformBloc(
          gh<_i600.GetDetailEformUseCase>(),
          gh<_i599.ApprovingEformUseCase>(),
        ));
    gh.factory<_i608.GetAddressNearLocationGoogleUseCase>(() =>
        _i608.GetAddressNearLocationGoogleUseCase(
            gh<_i349.LocationGoogleRepository>()));
    gh.factory<_i609.GetLocationGoogleUseCase>(() =>
        _i609.GetLocationGoogleUseCase(gh<_i349.LocationGoogleRepository>()));
    gh.factory<_i610.GetAddressAddressLocationGoogleUseCase>(() =>
        _i610.GetAddressAddressLocationGoogleUseCase(
            gh<_i349.LocationGoogleRepository>()));
    gh.factory<_i611.GetAddressSearchLocationGoogleUseCase>(() =>
        _i611.GetAddressSearchLocationGoogleUseCase(
            gh<_i349.LocationGoogleRepository>()));
    gh.factory<_i612.SaveHrOrganizationUseCase>(() =>
        _i612.SaveHrOrganizationUseCase(gh<_i564.HrOrganizationRepository>()));
    gh.factory<_i613.GetHrOrganizationUseCase>(() =>
        _i613.GetHrOrganizationUseCase(gh<_i564.HrOrganizationRepository>()));
    gh.factory<_i614.GetSavedHrOrganizationUseCase>(() =>
        _i614.GetSavedHrOrganizationUseCase(
            gh<_i564.HrOrganizationRepository>()));
    gh.factory<_i615.RemoveHrOrganizationUseCase>(() =>
        _i615.RemoveHrOrganizationUseCase(
            gh<_i564.HrOrganizationRepository>()));
    gh.factory<_i616.DetailNewsBloc>(
        () => _i616.DetailNewsBloc(gh<_i590.GetDetailNewsUseCase>()));
    gh.factory<_i617.EmployeeGetBranchSelectionUseCase>(() =>
        _i617.EmployeeGetBranchSelectionUseCase(
            gh<_i197.BranchSelectionRepository>()));
    gh.factory<_i618.RemoveBranchSelectionUseCase>(() =>
        _i618.RemoveBranchSelectionUseCase(
            gh<_i197.BranchSelectionRepository>()));
    gh.factory<_i619.SaveBranchSelectionUseCase>(() =>
        _i619.SaveBranchSelectionUseCase(
            gh<_i197.BranchSelectionRepository>()));
    gh.factory<_i620.BedSelectBranchSelectionUseCase>(() =>
        _i620.BedSelectBranchSelectionUseCase(
            gh<_i197.BranchSelectionRepository>()));
    gh.factory<_i621.GetFloorBranchSelectionUseCase>(() =>
        _i621.GetFloorBranchSelectionUseCase(
            gh<_i197.BranchSelectionRepository>()));
    gh.factory<_i622.BedChangeBranchSelectionUseCase>(() =>
        _i622.BedChangeBranchSelectionUseCase(
            gh<_i197.BranchSelectionRepository>()));
    gh.factory<_i623.EstimateTimeGetBranchSelectionUseCase>(() =>
        _i623.EstimateTimeGetBranchSelectionUseCase(
            gh<_i197.BranchSelectionRepository>()));
    gh.factory<_i624.GetSavedBranchSelectionUseCase>(() =>
        _i624.GetSavedBranchSelectionUseCase(
            gh<_i197.BranchSelectionRepository>()));
    gh.factory<_i625.GetBedBranchSelectionUseCase>(() =>
        _i625.GetBedBranchSelectionUseCase(
            gh<_i197.BranchSelectionRepository>()));
    gh.factory<_i626.GetBranchSelectionUseCase>(() =>
        _i626.GetBranchSelectionUseCase(gh<_i197.BranchSelectionRepository>()));
    gh.factory<_i627.GetRoomBranchSelectionUseCase>(() =>
        _i627.GetRoomBranchSelectionUseCase(
            gh<_i197.BranchSelectionRepository>()));
    gh.factory<_i628.GetProvinceUseCase>(
        () => _i628.GetProvinceUseCase(gh<_i197.BranchSelectionRepository>()));
    gh.lazySingleton<_i629.ConsultationManagerRepository>(
        () => _i630.ConsultationManagerRepositoryImpl(
              gh<_i18.ConsultationManagerApiService>(),
              gh<_i23.EZCache>(),
            ));
    gh.factory<_i631.MedicalDepartmentListBloc>(
        () => _i631.MedicalDepartmentListBloc(
              gh<_i373.GetMedicalDepartmentListUseCase>(),
              gh<_i374.SaveMedicalDepartmentListUseCase>(),
              gh<_i372.GetSavedMedicalDepartmentListUseCase>(),
              gh<_i375.RemoveMedicalDepartmentListUseCase>(),
            ));
    gh.factory<_i632.PostReadNotificationUseCase>(() =>
        _i632.PostReadNotificationUseCase(gh<_i483.NotificationRepository>()));
    gh.factory<_i633.GetDetailNotificationUseCase>(() =>
        _i633.GetDetailNotificationUseCase(gh<_i483.NotificationRepository>()));
    gh.factory<_i634.GetNavigationInfoUseCase>(() =>
        _i634.GetNavigationInfoUseCase(gh<_i483.NotificationRepository>()));
    gh.factory<_i635.SearchNotificationsUseCase>(() =>
        _i635.SearchNotificationsUseCase(gh<_i483.NotificationRepository>()));
    gh.factory<_i636.GetNotificationsUseCase>(() =>
        _i636.GetNotificationsUseCase(gh<_i483.NotificationRepository>()));
    gh.factory<_i637.ReadAllNotificationUseCase>(() =>
        _i637.ReadAllNotificationUseCase(gh<_i483.NotificationRepository>()));
    gh.lazySingleton<_i638.AssignTaskRepository>(
        () => _i639.AssignTaskRepositoryImpl(
              gh<_i18.AssignTaskApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.lazySingleton<_i640.MedicalServiceLogListRepository>(
        () => _i641.MedicalServiceLogListRepositoryImpl(
              gh<_i18.MedicalServiceLogListApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.factory<_i642.SuggestServicesFetchCustomerBookingInfoUseCase>(() =>
        _i642.SuggestServicesFetchCustomerBookingInfoUseCase(
            gh<_i378.CustomerBookingInfoRepository>()));
    gh.factory<_i643.RemoveCustomerBookingInfoUseCase>(() =>
        _i643.RemoveCustomerBookingInfoUseCase(
            gh<_i378.CustomerBookingInfoRepository>()));
    gh.factory<_i644.ServiceDetailsLoadCustomerBookingInfoUseCase>(() =>
        _i644.ServiceDetailsLoadCustomerBookingInfoUseCase(
            gh<_i378.CustomerBookingInfoRepository>()));
    gh.factory<_i645.SaveCustomerBookingInfoUseCase>(() =>
        _i645.SaveCustomerBookingInfoUseCase(
            gh<_i378.CustomerBookingInfoRepository>()));
    gh.factory<_i646.GetCustomerBookingInfoUseCase>(() =>
        _i646.GetCustomerBookingInfoUseCase(
            gh<_i378.CustomerBookingInfoRepository>()));
    gh.factory<_i647.BookedServicesFetchCustomerBookingInfoUseCase>(() =>
        _i647.BookedServicesFetchCustomerBookingInfoUseCase(
            gh<_i378.CustomerBookingInfoRepository>()));
    gh.factory<_i648.GetSavedCustomerBookingInfoUseCase>(() =>
        _i648.GetSavedCustomerBookingInfoUseCase(
            gh<_i378.CustomerBookingInfoRepository>()));
    gh.factory<_i649.UsedServiceFetchCustomerBookingInfoUseCase>(() =>
        _i649.UsedServiceFetchCustomerBookingInfoUseCase(
            gh<_i378.CustomerBookingInfoRepository>()));
    gh.factory<_i650.UploadFeedbackUseCase>(
        () => _i650.UploadFeedbackUseCase(gh<_i376.MediaUploadRepository>()));
    gh.factory<_i651.UploadCheckInImageUseCase>(() =>
        _i651.UploadCheckInImageUseCase(gh<_i376.MediaUploadRepository>()));
    gh.factory<_i652.UploadAvatarUseCase>(
        () => _i652.UploadAvatarUseCase(gh<_i376.MediaUploadRepository>()));
    gh.factory<_i653.UploadBackgroundUseCase>(
        () => _i653.UploadBackgroundUseCase(gh<_i376.MediaUploadRepository>()));
    gh.factory<_i654.UploadKYCUseCase>(
        () => _i654.UploadKYCUseCase(gh<_i376.MediaUploadRepository>()));
    gh.factory<_i655.DoctorFetchServiceDetailUseCase>(() =>
        _i655.DoctorFetchServiceDetailUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i656.EmployeeFetchServiceDetailUseCase>(() =>
        _i656.EmployeeFetchServiceDetailUseCase(
            gh<_i267.ConsultationCustomerRepository>()));
    gh.factory<_i657.SendKycPhotosSettingUseCase>(
        () => _i657.SendKycPhotosSettingUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i658.CheckinPermissionCheckUserUseCase>(() =>
        _i658.CheckinPermissionCheckUserUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i659.StringeeTokenFetchUserUseCase>(
        () => _i659.StringeeTokenFetchUserUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i660.CheckPermissionUserUseCase>(
        () => _i660.CheckPermissionUserUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i661.UserDeletionUseCase>(
        () => _i661.UserDeletionUseCase(gh<_i265.UserRepository>()));
    gh.lazySingleton<_i662.MedicalTemplateListRepository>(
        () => _i663.MedicalTemplateListRepositoryImpl(
              gh<_i18.MedicalTemplateListApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.factory<_i664.UpdateCommentUseCase>(
        () => _i664.UpdateCommentUseCase(gh<_i466.CommentListRepository>()));
    gh.factory<_i665.GetCheckinPhotoUseCase>(
        () => _i665.GetCheckinPhotoUseCase(gh<_i251.CheckinPhotoRepository>()));
    gh.factory<_i666.GetSavedCheckinPhotoUseCase>(() =>
        _i666.GetSavedCheckinPhotoUseCase(gh<_i251.CheckinPhotoRepository>()));
    gh.factory<_i667.SaveCheckinPhotoUseCase>(() =>
        _i667.SaveCheckinPhotoUseCase(gh<_i251.CheckinPhotoRepository>()));
    gh.factory<_i668.RemoveCheckinPhotoUseCase>(() =>
        _i668.RemoveCheckinPhotoUseCase(gh<_i251.CheckinPhotoRepository>()));
    gh.factory<_i669.DetailStaffEvaluationPeriodBloc>(
        () => _i669.DetailStaffEvaluationPeriodBloc(
              gh<_i481.GetDetailStaffEvaluationPeriodUseCase>(),
              gh<_i477.SaveDetailStaffEvaluationPeriodUseCase>(),
              gh<_i480.GetSavedDetailStaffEvaluationPeriodUseCase>(),
              gh<_i479.RemoveDetailStaffEvaluationPeriodUseCase>(),
              gh<_i478.EmployeeFetchDetailStaffEvaluationPeriodUseCase>(),
            ));
    gh.factory<_i670.HomeFindBloc>(() =>
        _i670.HomeFindBloc(gh<_i656.EmployeeFetchServiceDetailUseCase>()));
    gh.factory<_i671.ThemeBloc>(() => _i671.ThemeBloc(
          gh<_i407.GetThemeOptionUseCase>(),
          gh<_i409.SaveThemeOptionUseCase>(),
        ));
    gh.factory<_i672.GetBranchChatListUseCase>(() =>
        _i672.GetBranchChatListUseCase(gh<_i416.BranchChatListRepository>()));
    gh.factory<_i673.GetSavedBranchChatListUseCase>(() =>
        _i673.GetSavedBranchChatListUseCase(
            gh<_i416.BranchChatListRepository>()));
    gh.factory<_i674.RemoveBranchChatListUseCase>(() =>
        _i674.RemoveBranchChatListUseCase(
            gh<_i416.BranchChatListRepository>()));
    gh.factory<_i675.SaveBranchChatListUseCase>(() =>
        _i675.SaveBranchChatListUseCase(gh<_i416.BranchChatListRepository>()));
    gh.lazySingleton<_i676.RequestRepository>(() => _i677.RequestRepositoryImpl(
          gh<_i9.RequestApiService>(),
          cache: gh<_i9.EZCache>(),
        ));
    gh.factory<_i678.TabBarBloc>(() => _i678.TabBarBloc(
          gh<_i455.GetStoryListUseCase>(),
          gh<_i551.GetServicesUseCase>(),
        ));
    gh.factory<_i679.ImportantNotesBloc>(() => _i679.ImportantNotesBloc(
          gh<_i495.GetImportantNotesUseCase>(),
          gh<_i493.SaveImportantNotesUseCase>(),
          gh<_i492.GetSavedImportantNotesUseCase>(),
          gh<_i496.RemoveImportantNotesUseCase>(),
        ));
    gh.factory<_i680.SaveCustomerInfoDetailsUseCase>(() =>
        _i680.SaveCustomerInfoDetailsUseCase(
            gh<_i562.CustomerInfoDetailsRepository>()));
    gh.factory<_i681.GetCustomerInfoDetailsUseCase>(() =>
        _i681.GetCustomerInfoDetailsUseCase(
            gh<_i562.CustomerInfoDetailsRepository>()));
    gh.factory<_i682.CheckoutCustomerInfoDetailsUseCase>(() =>
        _i682.CheckoutCustomerInfoDetailsUseCase(
            gh<_i562.CustomerInfoDetailsRepository>()));
    gh.factory<_i683.GetSavedCustomerInfoDetailsUseCase>(() =>
        _i683.GetSavedCustomerInfoDetailsUseCase(
            gh<_i562.CustomerInfoDetailsRepository>()));
    gh.factory<_i684.RemoveCustomerInfoDetailsUseCase>(() =>
        _i684.RemoveCustomerInfoDetailsUseCase(
            gh<_i562.CustomerInfoDetailsRepository>()));
    gh.factory<_i685.CustomerRecordBloc>(() => _i685.CustomerRecordBloc(
          gh<_i448.GetCustomerRecordUseCase>(),
          gh<_i449.SaveCustomerRecordUseCase>(),
          gh<_i447.GetSavedCustomerRecordUseCase>(),
          gh<_i446.RemoveCustomerRecordUseCase>(),
        ));
    gh.lazySingleton<_i686.MedicalServiceCreationRepository>(
        () => _i687.MedicalServiceCreationRepositoryImpl(
              gh<_i18.MedicalServiceCreationApiService>(),
              gh<_i9.EZCache>(),
            ));
    gh.lazySingleton<_i688.TagImageRepository>(
        () => _i689.TagImageRepositoryImpl(gh<_i190.TagImageApiService>()));
    gh.factory<_i690.GetKpiEmployeeDetailUseCase>(() =>
        _i690.GetKpiEmployeeDetailUseCase(gh<_i515.KpiEmployeeRepository>()));
    gh.factory<_i691.GetKpiEmployeeUseCase>(
        () => _i691.GetKpiEmployeeUseCase(gh<_i515.KpiEmployeeRepository>()));
    gh.factory<_i692.CreateConsultationCustomerProfileUseCase>(() =>
        _i692.CreateConsultationCustomerProfileUseCase(
            gh<_i401.CustomerProfileRepository>()));
    gh.factory<_i693.SaveCustomerProfileUseCase>(() =>
        _i693.SaveCustomerProfileUseCase(
            gh<_i401.CustomerProfileRepository>()));
    gh.factory<_i694.RemoveCustomerProfileUseCase>(() =>
        _i694.RemoveCustomerProfileUseCase(
            gh<_i401.CustomerProfileRepository>()));
    gh.factory<_i695.GetConsultationHistoryCustomerProfileUseCase>(() =>
        _i695.GetConsultationHistoryCustomerProfileUseCase(
            gh<_i401.CustomerProfileRepository>()));
    gh.factory<_i696.GetCustomerProfileUseCase>(() =>
        _i696.GetCustomerProfileUseCase(gh<_i401.CustomerProfileRepository>()));
    gh.factory<_i697.GetSavedCustomerProfileUseCase>(() =>
        _i697.GetSavedCustomerProfileUseCase(
            gh<_i401.CustomerProfileRepository>()));
    gh.factory<_i698.UpdateConsultationCustomerProfileUseCase>(() =>
        _i698.UpdateConsultationCustomerProfileUseCase(
            gh<_i401.CustomerProfileRepository>()));
    gh.factory<_i699.SubmitRatingHumanUseCase>(() =>
        _i699.SubmitRatingHumanUseCase(gh<_i257.RatingHumanRepository>()));
    gh.factory<_i700.GetQuestionDetailUseCase>(() =>
        _i700.GetQuestionDetailUseCase(gh<_i257.RatingHumanRepository>()));
    gh.factory<_i701.SaveRatingHumanUseCase>(
        () => _i701.SaveRatingHumanUseCase(gh<_i257.RatingHumanRepository>()));
    gh.factory<_i702.GetRatingHumanUseCase>(
        () => _i702.GetRatingHumanUseCase(gh<_i257.RatingHumanRepository>()));
    gh.factory<_i703.HrOrganizationBloc>(() => _i703.HrOrganizationBloc(
          gh<_i613.GetHrOrganizationUseCase>(),
          gh<_i612.SaveHrOrganizationUseCase>(),
          gh<_i614.GetSavedHrOrganizationUseCase>(),
          gh<_i615.RemoveHrOrganizationUseCase>(),
        ));
    gh.factory<_i704.RemoveMedicineDetailUseCase>(() =>
        _i704.RemoveMedicineDetailUseCase(
            gh<_i209.MedicineDetailRepository>()));
    gh.factory<_i705.GetUnitMedicineDetailUseCase>(() =>
        _i705.GetUnitMedicineDetailUseCase(
            gh<_i209.MedicineDetailRepository>()));
    gh.factory<_i706.CreateMedicineDetailUseCase>(() =>
        _i706.CreateMedicineDetailUseCase(
            gh<_i209.MedicineDetailRepository>()));
    gh.factory<_i707.UpdateMedicineDetailUseCase>(() =>
        _i707.UpdateMedicineDetailUseCase(
            gh<_i209.MedicineDetailRepository>()));
    gh.factory<_i708.GetSavedMedicineDetailUseCase>(() =>
        _i708.GetSavedMedicineDetailUseCase(
            gh<_i209.MedicineDetailRepository>()));
    gh.factory<_i709.GetMedicineDetailUseCase>(() =>
        _i709.GetMedicineDetailUseCase(gh<_i209.MedicineDetailRepository>()));
    gh.factory<_i710.SaveMedicineDetailUseCase>(() =>
        _i710.SaveMedicineDetailUseCase(gh<_i209.MedicineDetailRepository>()));
    gh.factory<_i711.SaveCustomerScheduleUseCase>(() =>
        _i711.SaveCustomerScheduleUseCase(
            gh<_i513.CustomerScheduleRepository>()));
    gh.factory<_i712.GetSavedCustomerScheduleUseCase>(() =>
        _i712.GetSavedCustomerScheduleUseCase(
            gh<_i513.CustomerScheduleRepository>()));
    gh.factory<_i713.RemoveCustomerScheduleUseCase>(() =>
        _i713.RemoveCustomerScheduleUseCase(
            gh<_i513.CustomerScheduleRepository>()));
    gh.factory<_i714.GetCustomerScheduleUseCase>(() =>
        _i714.GetCustomerScheduleUseCase(
            gh<_i513.CustomerScheduleRepository>()));
    gh.factory<_i715.GetSavedDevUseCase>(
        () => _i715.GetSavedDevUseCase(gh<_i347.DevRepository>()));
    gh.factory<_i716.GetDevUseCase>(
        () => _i716.GetDevUseCase(gh<_i347.DevRepository>()));
    gh.factory<_i717.SaveDevUseCase>(
        () => _i717.SaveDevUseCase(gh<_i347.DevRepository>()));
    gh.factory<_i718.MiniAppDevUseCase>(
        () => _i718.MiniAppDevUseCase(gh<_i347.DevRepository>()));
    gh.factory<_i719.RemoveDevUseCase>(
        () => _i719.RemoveDevUseCase(gh<_i347.DevRepository>()));
    gh.factory<_i720.RemoveMedicalServiceCreationUseCase>(() =>
        _i720.RemoveMedicalServiceCreationUseCase(
            gh<_i686.MedicalServiceCreationRepository>()));
    gh.factory<_i721.ServicesMedicalServiceCreationUseCase>(() =>
        _i721.ServicesMedicalServiceCreationUseCase(
            gh<_i686.MedicalServiceCreationRepository>()));
    gh.factory<_i722.MethodsMedicalServiceCreationUseCase>(() =>
        _i722.MethodsMedicalServiceCreationUseCase(
            gh<_i686.MedicalServiceCreationRepository>()));
    gh.factory<_i723.GetSavedMedicalServiceCreationUseCase>(() =>
        _i723.GetSavedMedicalServiceCreationUseCase(
            gh<_i686.MedicalServiceCreationRepository>()));
    gh.factory<_i724.SaveMedicalServiceCreationUseCase>(() =>
        _i724.SaveMedicalServiceCreationUseCase(
            gh<_i686.MedicalServiceCreationRepository>()));
    gh.factory<_i725.MedicalServiceCreationUseCase>(() =>
        _i725.MedicalServiceCreationUseCase(
            gh<_i686.MedicalServiceCreationRepository>()));
    gh.factory<_i726.SaveFeedbackUseCase>(
        () => _i726.SaveFeedbackUseCase(gh<_i270.FeedbackRepository>()));
    gh.factory<_i727.SendFeedbackUseCase>(
        () => _i727.SendFeedbackUseCase(gh<_i270.FeedbackRepository>()));
    gh.factory<_i728.GetSavedFeedbackUseCase>(
        () => _i728.GetSavedFeedbackUseCase(gh<_i270.FeedbackRepository>()));
    gh.factory<_i729.RemoveFeedbackUseCase>(
        () => _i729.RemoveFeedbackUseCase(gh<_i270.FeedbackRepository>()));
    gh.factory<_i730.MultiLanguageBloc>(() => _i730.MultiLanguageBloc(
          gh<_i406.GetLanguageOptionUseCase>(),
          gh<_i408.SaveLanguageOptionUseCase>(),
        ));
    gh.factory<_i731.EformBloc>(
        () => _i731.EformBloc(gh<_i601.GetEformUseCase>()));
    gh.factory<_i732.CreateNoteDetailsUseCase>(() =>
        _i732.CreateNoteDetailsUseCase(gh<_i413.NoteDetailsRepository>()));
    gh.factory<_i733.UpdateNoteDetailsUseCase>(() =>
        _i733.UpdateNoteDetailsUseCase(gh<_i413.NoteDetailsRepository>()));
    gh.factory<_i734.RemoveNoteDetailsUseCase>(() =>
        _i734.RemoveNoteDetailsUseCase(gh<_i413.NoteDetailsRepository>()));
    gh.factory<_i735.GetSavedNoteDetailsUseCase>(() =>
        _i735.GetSavedNoteDetailsUseCase(gh<_i413.NoteDetailsRepository>()));
    gh.factory<_i736.GetNoteDetailsUseCase>(
        () => _i736.GetNoteDetailsUseCase(gh<_i413.NoteDetailsRepository>()));
    gh.factory<_i737.SaveNoteDetailsUseCase>(
        () => _i737.SaveNoteDetailsUseCase(gh<_i413.NoteDetailsRepository>()));
    gh.factory<_i738.SaveCustomerListUseCase>(() =>
        _i738.SaveCustomerListUseCase(gh<_i380.CustomerListRepository>()));
    gh.factory<_i739.GetCustomerListUseCase>(
        () => _i739.GetCustomerListUseCase(gh<_i380.CustomerListRepository>()));
    gh.factory<_i740.GetSavedCustomerListUseCase>(() =>
        _i740.GetSavedCustomerListUseCase(gh<_i380.CustomerListRepository>()));
    gh.factory<_i741.RemoveCustomerListUseCase>(() =>
        _i741.RemoveCustomerListUseCase(gh<_i380.CustomerListRepository>()));
    gh.factory<_i742.GetCustomerRelationShipListUseCase>(() =>
        _i742.GetCustomerRelationShipListUseCase(
            gh<_i380.CustomerListRepository>()));
    gh.factory<_i743.RemovePxUnasignedUseCase>(() =>
        _i743.RemovePxUnasignedUseCase(gh<_i440.PxUnasignedRepository>()));
    gh.factory<_i744.GetPxCustomerListUseCase>(() =>
        _i744.GetPxCustomerListUseCase(gh<_i440.PxUnasignedRepository>()));
    gh.factory<_i745.SavePxUnasignedUseCase>(
        () => _i745.SavePxUnasignedUseCase(gh<_i440.PxUnasignedRepository>()));
    gh.factory<_i746.GetSavedPxUnasignedUseCase>(() =>
        _i746.GetSavedPxUnasignedUseCase(gh<_i440.PxUnasignedRepository>()));
    gh.factory<_i747.LikeListBloc>(
        () => _i747.LikeListBloc(gh<_i388.GetLikeListUseCase>()));
    gh.factory<_i748.TicketActiveBloc>(
        () => _i748.TicketActiveBloc(gh<_i605.GetTicketActiveUseCase>()));
    gh.factory<_i749.ChatListBloc>(() => _i749.ChatListBloc(
          gh<_i162.GetChatListUseCase>(),
          gh<_i165.SaveChatListUseCase>(),
          gh<_i155.GetSavedChatListUseCase>(),
          gh<_i164.RemoveChatListUseCase>(),
          gh<_i157.SearchChatListUseCase>(),
          gh<_i159.SearchMessageChatListUseCase>(),
          gh<_i156.PinConversationChatListUseCase>(),
          gh<_i154.GetTotalUnreadChatListUseCase>(),
          gh<_i325.LoadCreateChatFolderUseCase>(),
          gh<_i328.ConversationLoadCreateChatFolderUseCase>(),
          gh<_i158.MarkAsReadChatListUseCase>(),
          gh<_i161.GetRecentContactsChatListUseCase>(),
          gh<_i163.UpdatePinConversationChatListUseCase>(),
          gh<_i152.SortFolderChatListUseCase>(),
        ));
    gh.factory<_i750.UpdateProfileUseCase>(
        () => _i750.UpdateProfileUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i751.CheckEmployeeUseCase>(
        () => _i751.CheckEmployeeUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i752.GetOtpUseCase>(
        () => _i752.GetOtpUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i753.CheckPhoneUseCase>(
        () => _i753.CheckPhoneUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i754.LoginUseCase>(
        () => _i754.LoginUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i755.LogoutUseCase>(
        () => _i755.LogoutUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i756.UploadImagesUseCase>(
        () => _i756.UploadImagesUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i757.GetProfilesUseCase>(
        () => _i757.GetProfilesUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i758.GetEnableOnlineLoggerUseCase>(
        () => _i758.GetEnableOnlineLoggerUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i759.GetProfileUseCase>(
        () => _i759.GetProfileUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i760.ResetPasswordUseCase>(
        () => _i760.ResetPasswordUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i761.UploadFilesUseCase>(
        () => _i761.UploadFilesUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i762.GetConfigurationUseCase>(
        () => _i762.GetConfigurationUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i763.ConfirmOtpUseCase>(
        () => _i763.ConfirmOtpUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i764.UpdateBioUseCase>(
        () => _i764.UpdateBioUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i765.LoginSocialUseCase>(
        () => _i765.LoginSocialUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i766.SubmitFeedbackUseCase>(
        () => _i766.SubmitFeedbackUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i767.UploadAudiosUseCase>(
        () => _i767.UploadAudiosUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i768.ChangePasswordUseCase>(
        () => _i768.ChangePasswordUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i769.UploadUseCase>(
        () => _i769.UploadUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i770.GetUserInfoUseCase>(
        () => _i770.GetUserInfoUseCase(gh<_i265.UserRepository>()));
    gh.lazySingleton<_i771.CacheQuickActionRemoveUseCase>(
        () => _i771.CacheQuickActionRemoveUseCase(gh<_i265.UserRepository>()));
    gh.lazySingleton<_i772.CacheQuickActionGetUseCase>(
        () => _i772.CacheQuickActionGetUseCase(gh<_i265.UserRepository>()));
    gh.lazySingleton<_i773.CacheQuickActionSaveUseCase>(
        () => _i773.CacheQuickActionSaveUseCase(gh<_i265.UserRepository>()));
    gh.lazySingleton<_i774.CacheLoginStringeeRemoveUseCase>(() =>
        _i774.CacheLoginStringeeRemoveUseCase(gh<_i265.UserRepository>()));
    gh.lazySingleton<_i775.IsShowBoardingUseCase>(
        () => _i775.IsShowBoardingUseCase(gh<_i265.UserRepository>()));
    gh.lazySingleton<_i776.HasTokenUseCase>(
        () => _i776.HasTokenUseCase(gh<_i265.UserRepository>()));
    gh.lazySingleton<_i777.CacheLoginStringeeGetUseCase>(
        () => _i777.CacheLoginStringeeGetUseCase(gh<_i265.UserRepository>()));
    gh.lazySingleton<_i778.CacheUserUseCase>(
        () => _i778.CacheUserUseCase(gh<_i265.UserRepository>()));
    gh.lazySingleton<_i779.CacheLoginStringeeSaveUseCase>(
        () => _i779.CacheLoginStringeeSaveUseCase(gh<_i265.UserRepository>()));
    gh.lazySingleton<_i780.CacheCheckoutHourSaveUseCase>(
        () => _i780.CacheCheckoutHourSaveUseCase(gh<_i265.UserRepository>()));
    gh.lazySingleton<_i781.CacheCheckinHourSaveUseCase>(
        () => _i781.CacheCheckinHourSaveUseCase(gh<_i265.UserRepository>()));
    gh.lazySingleton<_i782.CacheCheckoutHourGetUseCase>(
        () => _i782.CacheCheckoutHourGetUseCase(gh<_i265.UserRepository>()));
    gh.lazySingleton<_i783.CacheCheckinHourGetUseCase>(
        () => _i783.CacheCheckinHourGetUseCase(gh<_i265.UserRepository>()));
    gh.factory<_i784.DevBloc>(() => _i784.DevBloc(
          gh<_i716.GetDevUseCase>(),
          gh<_i717.SaveDevUseCase>(),
          gh<_i715.GetSavedDevUseCase>(),
          gh<_i719.RemoveDevUseCase>(),
          gh<_i718.MiniAppDevUseCase>(),
        ));
    gh.factory<_i785.ProductDetailConfirmBloc>(
        () => _i785.ProductDetailConfirmBloc(
              gh<_i583.GetProductDetailConfirmUseCase>(),
              gh<_i584.ApprovalProductDetailConfirmUsecase>(),
              gh<_i586.RejectProductDetailConfirmUsecase>(),
            ));
    gh.lazySingleton<_i786.CollaboratorUserBloc>(
        () => _i786.CollaboratorUserBloc(gh<_i778.CacheUserUseCase>()));
    gh.factory<_i787.ScheduleDetailsBloc>(() => _i787.ScheduleDetailsBloc(
          gh<_i569.GetScheduleDetailsUseCase>(),
          gh<_i567.SaveScheduleDetailsUseCase>(),
          gh<_i568.GetSavedScheduleDetailsUseCase>(),
          gh<_i566.RemoveScheduleDetailsUseCase>(),
        ));
    gh.factory<_i788.NotiBotTypePostTakingCareCustomerUseCase>(() =>
        _i788.NotiBotTypePostTakingCareCustomerUseCase(
            gh<_i370.TakingCareCustomerRepository>()));
    gh.factory<_i789.RemoveImageTakingCareCustomerUseCase>(() =>
        _i789.RemoveImageTakingCareCustomerUseCase(
            gh<_i370.TakingCareCustomerRepository>()));
    gh.factory<_i790.FinishTaskTakingCareCustomerUseCase>(() =>
        _i790.FinishTaskTakingCareCustomerUseCase(
            gh<_i370.TakingCareCustomerRepository>()));
    gh.factory<_i791.GetSavedTakingCareCustomerUseCase>(() =>
        _i791.GetSavedTakingCareCustomerUseCase(
            gh<_i370.TakingCareCustomerRepository>()));
    gh.factory<_i792.CreateSupportTakingCareCustomerUseCase>(() =>
        _i792.CreateSupportTakingCareCustomerUseCase(
            gh<_i370.TakingCareCustomerRepository>()));
    gh.factory<_i793.GetSectionTakingCareCustomerUseCase>(() =>
        _i793.GetSectionTakingCareCustomerUseCase(
            gh<_i370.TakingCareCustomerRepository>()));
    gh.factory<_i794.RemoveTakingCareCustomerUseCase>(() =>
        _i794.RemoveTakingCareCustomerUseCase(
            gh<_i370.TakingCareCustomerRepository>()));
    gh.factory<_i795.SaveTakingCareCustomerUseCase>(() =>
        _i795.SaveTakingCareCustomerUseCase(
            gh<_i370.TakingCareCustomerRepository>()));
    gh.factory<_i796.CreateTreatmentDetailsTakingCareCustomerUseCase>(() =>
        _i796.CreateTreatmentDetailsTakingCareCustomerUseCase(
            gh<_i370.TakingCareCustomerRepository>()));
    gh.factory<_i797.CheckEmployeeInRoomTakingCareCustomerUseCase>(() =>
        _i797.CheckEmployeeInRoomTakingCareCustomerUseCase(
            gh<_i370.TakingCareCustomerRepository>()));
    gh.factory<_i798.UpdateServiceDetailUseCaseTakingCareCustomerUseCase>(() =>
        _i798.UpdateServiceDetailUseCaseTakingCareCustomerUseCase(
            gh<_i370.TakingCareCustomerRepository>()));
    gh.factory<_i799.GetTakingCareCustomerUseCase>(() =>
        _i799.GetTakingCareCustomerUseCase(
            gh<_i370.TakingCareCustomerRepository>()));
    gh.factory<_i800.GetTreatmentPhotoTakingCareCustomerUseCase>(() =>
        _i800.GetTreatmentPhotoTakingCareCustomerUseCase(
            gh<_i370.TakingCareCustomerRepository>()));
    gh.factory<_i801.BotTypeLoadTakingCareCustomerUseCase>(() =>
        _i801.BotTypeLoadTakingCareCustomerUseCase(
            gh<_i370.TakingCareCustomerRepository>()));
    gh.factory<_i802.UploadRecordTakingCareCustomerUseCase>(() =>
        _i802.UploadRecordTakingCareCustomerUseCase(
            gh<_i370.TakingCareCustomerRepository>()));
    gh.factory<_i803.UploadImagesTakingCareCustomerUseCase>(() =>
        _i803.UploadImagesTakingCareCustomerUseCase(
            gh<_i370.TakingCareCustomerRepository>()));
    gh.factory<_i804.GetSavedPxListUseCase>(
        () => _i804.GetSavedPxListUseCase(gh<_i343.PxListRepository>()));
    gh.factory<_i805.GetPxListUseCase>(
        () => _i805.GetPxListUseCase(gh<_i343.PxListRepository>()));
    gh.factory<_i806.SavePxListUseCase>(
        () => _i806.SavePxListUseCase(gh<_i343.PxListRepository>()));
    gh.factory<_i807.RemovePxListUseCase>(
        () => _i807.RemovePxListUseCase(gh<_i343.PxListRepository>()));
    gh.factory<_i808.SaveMedicalServiceLogListUseCase>(() =>
        _i808.SaveMedicalServiceLogListUseCase(
            gh<_i640.MedicalServiceLogListRepository>()));
    gh.factory<_i809.RemoveMedicalServiceLogListUseCase>(() =>
        _i809.RemoveMedicalServiceLogListUseCase(
            gh<_i640.MedicalServiceLogListRepository>()));
    gh.factory<_i810.GetMedicalServiceLogListUseCase>(() =>
        _i810.GetMedicalServiceLogListUseCase(
            gh<_i640.MedicalServiceLogListRepository>()));
    gh.factory<_i811.GetSavedMedicalServiceLogListUseCase>(() =>
        _i811.GetSavedMedicalServiceLogListUseCase(
            gh<_i640.MedicalServiceLogListRepository>()));
    gh.factory<_i812.GroupChatDetailBloc>(() => _i812.GroupChatDetailBloc(
          gh<_i279.GetGroupChatDetailUseCase>(),
          gh<_i288.SaveGroupChatDetailUseCase>(),
          gh<_i287.GetSavedGroupChatDetailUseCase>(),
          gh<_i274.RemoveGroupChatDetailUseCase>(),
          gh<_i277.MediaLoadGroupChatDetailUseCase>(),
          gh<_i284.FileLoadGroupChatDetailUseCase>(),
          gh<_i281.LinkLoadGroupChatDetailUseCase>(),
          gh<_i286.UpdateGroupChatDetailUseCase>(),
          gh<_i273.AvatarUploadGroupChatDetailUseCase>(),
          gh<_i757.GetProfilesUseCase>(),
          gh<_i276.DeleteGroupUseCase>(),
          gh<_i275.MemberInfoLoadGroupChatDetailUseCase>(),
          gh<_i278.UpdateMemberRuleGroupChatDetailUseCase>(),
          gh<_i282.UpdateAdminRuleGroupChatDetailUseCase>(),
          gh<_i283.GetUserRulesGroupChatDetailUseCase>(),
          gh<_i285.GetRuleByRoleGroupChatDetailUseCase>(),
          gh<_i280.ChangeOwnerGroupChatDetailUseCase>(),
          gh<_i272.GetUserExceptionGroupChatDetailUseCase>(),
        ));
    gh.factory<_i813.CheckinReminderBloc>(() => _i813.CheckinReminderBloc(
          gh<_i783.CacheCheckinHourGetUseCase>(),
          gh<_i781.CacheCheckinHourSaveUseCase>(),
          gh<_i782.CacheCheckoutHourGetUseCase>(),
          gh<_i780.CacheCheckoutHourSaveUseCase>(),
        ));
    gh.factory<_i814.CheckLeaderHomeUseCase>(
        () => _i814.CheckLeaderHomeUseCase(gh<_i452.HomeRepository>()));
    gh.factory<_i815.WorkLeaderCheckHomeUseCase>(
        () => _i815.WorkLeaderCheckHomeUseCase(gh<_i452.HomeRepository>()));
    gh.factory<_i816.CheckinCustomerUseCase>(
        () => _i816.CheckinCustomerUseCase(gh<_i289.CustomerRepository>()));
    gh.factory<_i817.GetRoomListCustomerUseCase>(
        () => _i817.GetRoomListCustomerUseCase(gh<_i289.CustomerRepository>()));
    gh.factory<_i818.SaveCustomerRoomCodeUseCase>(() =>
        _i818.SaveCustomerRoomCodeUseCase(gh<_i289.CustomerRepository>()));
    gh.factory<_i819.PrintCustomerUseCase>(
        () => _i819.PrintCustomerUseCase(gh<_i289.CustomerRepository>()));
    gh.factory<_i820.StickerBloc>(() => _i820.StickerBloc(
          gh<_i339.CreateStickerListUseCase>(),
          gh<_i341.GetStickerListUseCase>(),
          gh<_i338.GetStickerSetUseCase>(),
          gh<_i335.GetStickerRecentUseCase>(),
          gh<_i340.UploadStickerUseCase>(),
          gh<_i337.UpdateStickerRecentUseCase>(),
          gh<_i334.GetStickerOnlySetUseCase>(),
          gh<_i342.RemoveStickerSetUseCase>(),
          gh<_i336.RemoveStickerUseCase>(),
          gh<_i333.UploadStickerSetUseCase>(),
        ));
    gh.factory<_i821.NoteDetailsBloc>(() => _i821.NoteDetailsBloc(
          gh<_i736.GetNoteDetailsUseCase>(),
          gh<_i737.SaveNoteDetailsUseCase>(),
          gh<_i735.GetSavedNoteDetailsUseCase>(),
          gh<_i734.RemoveNoteDetailsUseCase>(),
          gh<_i732.CreateNoteDetailsUseCase>(),
          gh<_i733.UpdateNoteDetailsUseCase>(),
          gh<_i756.UploadImagesUseCase>(),
        ));
    gh.factory<_i822.TakingCareCustomerBloc>(() => _i822.TakingCareCustomerBloc(
          gh<_i799.GetTakingCareCustomerUseCase>(),
          gh<_i795.SaveTakingCareCustomerUseCase>(),
          gh<_i791.GetSavedTakingCareCustomerUseCase>(),
          gh<_i794.RemoveTakingCareCustomerUseCase>(),
          gh<_i790.FinishTaskTakingCareCustomerUseCase>(),
          gh<_i803.UploadImagesTakingCareCustomerUseCase>(),
          gh<_i789.RemoveImageTakingCareCustomerUseCase>(),
          gh<_i802.UploadRecordTakingCareCustomerUseCase>(),
          gh<_i792.CreateSupportTakingCareCustomerUseCase>(),
          gh<_i797.CheckEmployeeInRoomTakingCareCustomerUseCase>(),
          gh<_i801.BotTypeLoadTakingCareCustomerUseCase>(),
          gh<_i298.ProductLoadConsultationCustomerUseCase>(),
          gh<_i319.GetTreatmentDetailUseCase>(),
          gh<_i788.NotiBotTypePostTakingCareCustomerUseCase>(),
          gh<_i800.GetTreatmentPhotoTakingCareCustomerUseCase>(),
          gh<_i798.UpdateServiceDetailUseCaseTakingCareCustomerUseCase>(),
        ));
    gh.factory<_i823.GetComboTagUsecase>(
        () => _i823.GetComboTagUsecase(gh<_i688.TagImageRepository>()));
    gh.factory<_i824.GetImageByComboTagUsecae>(
        () => _i824.GetImageByComboTagUsecae(gh<_i688.TagImageRepository>()));
    gh.factory<_i825.StoryDetailBloc>(() => _i825.StoryDetailBloc(
          gh<_i174.GetStoryDetailUseCase>(),
          gh<_i464.DeleteStoryUseCase>(),
          gh<_i386.PostLikeStoryUseCase>(),
          gh<_i460.UpdateStoryUseCase>(),
        ));
    gh.factory<_i826.GetSavedUserListUseCase>(
        () => _i826.GetSavedUserListUseCase(gh<_i549.UserListRepository>()));
    gh.factory<_i827.GetUserListUseCase>(
        () => _i827.GetUserListUseCase(gh<_i549.UserListRepository>()));
    gh.factory<_i828.RemoveUserListUseCase>(
        () => _i828.RemoveUserListUseCase(gh<_i549.UserListRepository>()));
    gh.factory<_i829.SaveUserListUseCase>(
        () => _i829.SaveUserListUseCase(gh<_i549.UserListRepository>()));
    gh.factory<_i830.GetCommentListUseCase>(
        () => _i830.GetCommentListUseCase(gh<_i466.CommentListRepository>()));
    gh.factory<_i831.PostCommentUseCase>(
        () => _i831.PostCommentUseCase(gh<_i466.CommentListRepository>()));
    gh.factory<_i832.DeleteCommentUseCase>(
        () => _i832.DeleteCommentUseCase(gh<_i466.CommentListRepository>()));
    gh.factory<_i833.CommentUploadFileUseCase>(() =>
        _i833.CommentUploadFileUseCase(gh<_i466.CommentListRepository>()));
    gh.factory<_i834.CreatingEformBloc>(
        () => _i834.CreatingEformBloc(gh<_i594.CreateEformUseCase>()));
    gh.factory<_i835.BranchSelectionBloc>(() => _i835.BranchSelectionBloc(
          gh<_i626.GetBranchSelectionUseCase>(),
          gh<_i619.SaveBranchSelectionUseCase>(),
          gh<_i624.GetSavedBranchSelectionUseCase>(),
          gh<_i618.RemoveBranchSelectionUseCase>(),
          gh<_i628.GetProvinceUseCase>(),
          gh<_i621.GetFloorBranchSelectionUseCase>(),
        ));
    gh.factory<_i836.StaffEvaluationPeriodsBloc>(
        () => _i836.StaffEvaluationPeriodsBloc(
              gh<_i502.GetStaffEvaluationPeriodsUseCase>(),
              gh<_i500.SaveStaffEvaluationPeriodsUseCase>(),
              gh<_i501.GetSavedStaffEvaluationPeriodsUseCase>(),
              gh<_i499.RemoveStaffEvaluationPeriodsUseCase>(),
            ));
    gh.factory<_i837.SelectingOfficeBloc>(
        () => _i837.SelectingOfficeBloc(gh<_i591.GetDepartmentUseCase>()));
    gh.factory<_i838.GetChoicesUseCase>(
        () => _i838.GetChoicesUseCase(gh<_i393.CheckinRepository>()));
    gh.factory<_i839.RequestUpdateHistoryCheckinUseCase>(() =>
        _i839.RequestUpdateHistoryCheckinUseCase(
            gh<_i393.CheckinRepository>()));
    gh.factory<_i840.GetBranchesUseCase>(
        () => _i840.GetBranchesUseCase(gh<_i393.CheckinRepository>()));
    gh.factory<_i841.GetCheckinTypesUseCase>(
        () => _i841.GetCheckinTypesUseCase(gh<_i393.CheckinRepository>()));
    gh.factory<_i842.GetMonthlyHistoryCheckinUseCase>(() =>
        _i842.GetMonthlyHistoryCheckinUseCase(gh<_i393.CheckinRepository>()));
    gh.factory<_i843.KpiEmployeeBloc>(() => _i843.KpiEmployeeBloc(
          gh<_i691.GetKpiEmployeeUseCase>(),
          gh<_i690.GetKpiEmployeeDetailUseCase>(),
        ));
    gh.factory<_i844.UserListBloc>(() => _i844.UserListBloc(
          gh<_i829.SaveUserListUseCase>(),
          gh<_i826.GetSavedUserListUseCase>(),
          gh<_i828.RemoveUserListUseCase>(),
          gh<_i180.UserLoadCreateChatGroupUseCase>(),
          gh<_i286.UpdateGroupChatDetailUseCase>(),
        ));
    gh.factory<_i845.BedSelectionBloc>(() => _i845.BedSelectionBloc(
          gh<_i627.GetRoomBranchSelectionUseCase>(),
          gh<_i621.GetFloorBranchSelectionUseCase>(),
          gh<_i625.GetBedBranchSelectionUseCase>(),
          gh<_i620.BedSelectBranchSelectionUseCase>(),
          gh<_i622.BedChangeBranchSelectionUseCase>(),
          gh<_i617.EmployeeGetBranchSelectionUseCase>(),
          gh<_i623.EstimateTimeGetBranchSelectionUseCase>(),
        ));
    gh.factory<_i846.CreatingTaskBloc>(() => _i846.CreatingTaskBloc(
          gh<_i395.CreatingTaskUseCase>(),
          gh<_i769.UploadUseCase>(),
        ));
    gh.factory<_i847.AssignPxRecheckUpdateUseCase>(() =>
        _i847.AssignPxRecheckUpdateUseCase(gh<_i382.PxRecheckRepository>()));
    gh.factory<_i848.RefuseBloc>(
        () => _i848.RefuseBloc(gh<_i598.RejectEformUseCase>()));
    gh.factory<_i849.NotificationsBloc>(() => _i849.NotificationsBloc(
          gh<_i636.GetNotificationsUseCase>(),
          gh<_i635.SearchNotificationsUseCase>(),
          gh<_i637.ReadAllNotificationUseCase>(),
        ));
    gh.factory<_i850.ConfirmOTPBloc>(() => _i850.ConfirmOTPBloc(
          gh<_i763.ConfirmOtpUseCase>(),
          gh<_i752.GetOtpUseCase>(),
          gh<_i239.GetKeyAppsFlyerUseCase>(),
        ));
    gh.factory<_i851.GetStatusListCustomerUseCase>(() =>
        _i851.GetStatusListCustomerUseCase(gh<_i423.ListCustomerRepository>()));
    gh.factory<_i852.GetSavedListCustomerUseCase>(() =>
        _i852.GetSavedListCustomerUseCase(gh<_i423.ListCustomerRepository>()));
    gh.factory<_i853.GetListCustomerUseCase>(
        () => _i853.GetListCustomerUseCase(gh<_i423.ListCustomerRepository>()));
    gh.factory<_i854.SaveListCustomerUseCase>(() =>
        _i854.SaveListCustomerUseCase(gh<_i423.ListCustomerRepository>()));
    gh.factory<_i855.RemoveListCustomerUseCase>(() =>
        _i855.RemoveListCustomerUseCase(gh<_i423.ListCustomerRepository>()));
    gh.factory<_i856.SearchListCustomerUseCase>(() =>
        _i856.SearchListCustomerUseCase(gh<_i423.ListCustomerRepository>()));
    gh.factory<_i857.RemoveMedicalTemplateListUseCase>(() =>
        _i857.RemoveMedicalTemplateListUseCase(
            gh<_i662.MedicalTemplateListRepository>()));
    gh.factory<_i858.MedicalTemplateDetailGetMedicalTemplateListUseCase>(() =>
        _i858.MedicalTemplateDetailGetMedicalTemplateListUseCase(
            gh<_i662.MedicalTemplateListRepository>()));
    gh.factory<_i859.GetMedicalTemplateListUseCase>(() =>
        _i859.GetMedicalTemplateListUseCase(
            gh<_i662.MedicalTemplateListRepository>()));
    gh.factory<_i860.GetSavedMedicalTemplateListUseCase>(() =>
        _i860.GetSavedMedicalTemplateListUseCase(
            gh<_i662.MedicalTemplateListRepository>()));
    gh.factory<_i861.SaveMedicalTemplateListUseCase>(() =>
        _i861.SaveMedicalTemplateListUseCase(
            gh<_i662.MedicalTemplateListRepository>()));
    gh.factory<_i862.HaPointListGetMedicalLogDetailUseCase>(() =>
        _i862.HaPointListGetMedicalLogDetailUseCase(
            gh<_i506.MedicalLogDetailRepository>()));
    gh.factory<_i863.DoctorListGetMedicalLogDetailUseCase>(() =>
        _i863.DoctorListGetMedicalLogDetailUseCase(
            gh<_i506.MedicalLogDetailRepository>()));
    gh.factory<_i864.GetTattooColorMedicalLogDetailUseCase>(() =>
        _i864.GetTattooColorMedicalLogDetailUseCase(
            gh<_i506.MedicalLogDetailRepository>()));
    gh.factory<_i865.DosageListGetMedicalLogDetailUseCase>(() =>
        _i865.DosageListGetMedicalLogDetailUseCase(
            gh<_i506.MedicalLogDetailRepository>()));
    gh.factory<_i866.MedicineListGetMedicalLogDetailUseCase>(() =>
        _i866.MedicineListGetMedicalLogDetailUseCase(
            gh<_i506.MedicalLogDetailRepository>()));
    gh.factory<_i867.UpdateLogMedicalDetailUseCase>(() =>
        _i867.UpdateLogMedicalDetailUseCase(
            gh<_i506.MedicalLogDetailRepository>()));
    gh.factory<_i868.RemoveMedicalLogDetailUseCase>(() =>
        _i868.RemoveMedicalLogDetailUseCase(
            gh<_i506.MedicalLogDetailRepository>()));
    gh.factory<_i869.GetTattooTimeMedicalLogDetailUseCase>(() =>
        _i869.GetTattooTimeMedicalLogDetailUseCase(
            gh<_i506.MedicalLogDetailRepository>()));
    gh.factory<_i870.KhacnhoListGetMedicalLogDetailUseCase>(() =>
        _i870.KhacnhoListGetMedicalLogDetailUseCase(
            gh<_i506.MedicalLogDetailRepository>()));
    gh.factory<_i871.GetOriginStatusMedicalLogDetailUseCase>(() =>
        _i871.GetOriginStatusMedicalLogDetailUseCase(
            gh<_i506.MedicalLogDetailRepository>()));
    gh.factory<_i872.GetSavedMedicalLogDetailUseCase>(() =>
        _i872.GetSavedMedicalLogDetailUseCase(
            gh<_i506.MedicalLogDetailRepository>()));
    gh.factory<_i873.CreateLogMedicalDetailUseCase>(() =>
        _i873.CreateLogMedicalDetailUseCase(
            gh<_i506.MedicalLogDetailRepository>()));
    gh.factory<_i874.GetSkinMachineMedicalLogDetailUseCase>(() =>
        _i874.GetSkinMachineMedicalLogDetailUseCase(
            gh<_i506.MedicalLogDetailRepository>()));
    gh.factory<_i875.GetMedicalLogDetailUseCase>(() =>
        _i875.GetMedicalLogDetailUseCase(
            gh<_i506.MedicalLogDetailRepository>()));
    gh.factory<_i876.GetPostSaiMedicalLogDetailUseCase>(() =>
        _i876.GetPostSaiMedicalLogDetailUseCase(
            gh<_i506.MedicalLogDetailRepository>()));
    gh.factory<_i877.SaveMedicalLogDetailUseCase>(() =>
        _i877.SaveMedicalLogDetailUseCase(
            gh<_i506.MedicalLogDetailRepository>()));
    gh.factory<_i878.EformCategoryBloc>(
        () => _i878.EformCategoryBloc(gh<_i597.GetEformRequestTypeUseCase>()));
    gh.factory<_i879.NoteFinishPxRecheckUseCase>(() =>
        _i879.NoteFinishPxRecheckUseCase(gh<_i382.PxRecheckRepository>()));
    gh.factory<_i880.WorkStatusUpdatePxRecheckUseCase>(() =>
        _i880.WorkStatusUpdatePxRecheckUseCase(
            gh<_i382.PxRecheckRepository>()));
    gh.factory<_i881.AssignsFetchPxRecheckUseCase>(() =>
        _i881.AssignsFetchPxRecheckUseCase(gh<_i382.PxRecheckRepository>()));
    gh.factory<_i882.JobSchedulerBloc>(() => _i882.JobSchedulerBloc(
          gh<_i399.GetGeneralJobSchedulerUseCase>(),
          gh<_i397.GetJobSchedulerUseCase>(),
        ));
    gh.factory<_i883.CommentListBloc>(() => _i883.CommentListBloc(
          gh<_i830.GetCommentListUseCase>(),
          gh<_i833.CommentUploadFileUseCase>(),
          gh<_i831.PostCommentUseCase>(),
          gh<_i387.PostLikeCommentUseCase>(),
          gh<_i664.UpdateCommentUseCase>(),
          gh<_i832.DeleteCommentUseCase>(),
          gh<_i269.GetTagListUseCase>(),
        ));
    gh.factory<_i884.ListCustomerBloc>(() => _i884.ListCustomerBloc(
          gh<_i853.GetListCustomerUseCase>(),
          gh<_i854.SaveListCustomerUseCase>(),
          gh<_i852.GetSavedListCustomerUseCase>(),
          gh<_i855.RemoveListCustomerUseCase>(),
          gh<_i851.GetStatusListCustomerUseCase>(),
          gh<_i856.SearchListCustomerUseCase>(),
          gh<_i682.CheckoutCustomerInfoDetailsUseCase>(),
        ));
    gh.factory<_i885.StoryListBloc>(() => _i885.StoryListBloc(
          gh<_i455.GetStoryListUseCase>(),
          gh<_i457.PostStoryUseCase>(),
          gh<_i386.PostLikeStoryUseCase>(),
          gh<_i460.UpdateStoryUseCase>(),
          gh<_i464.DeleteStoryUseCase>(),
          gh<_i459.GetTotalNotificationUseCase>(),
          gh<_i456.GetStoryListSearchUseCase>(),
          gh<_i458.PutStoryVoteUseCase>(),
          gh<_i463.DeleteStoryVoteUseCase>(),
          gh<_i462.GetVoteUsersUseCase>(),
        ));
    gh.factory<_i886.DropDownStatusBloc>(
        () => _i886.DropDownStatusBloc(gh<_i442.GetDropDownStatusUseCase>()));
    gh.factory<_i887.BranchChatListBloc>(() => _i887.BranchChatListBloc(
          gh<_i672.GetBranchChatListUseCase>(),
          gh<_i675.SaveBranchChatListUseCase>(),
          gh<_i673.GetSavedBranchChatListUseCase>(),
          gh<_i674.RemoveBranchChatListUseCase>(),
        ));
    gh.factory<_i888.GetServiceAndProductActionsUseCase>(() =>
        _i888.GetServiceAndProductActionsUseCase(
            gh<_i497.ServiceAndProductRepository>()));
    gh.factory<_i889.GetServiceAndProductUseCase>(() =>
        _i889.GetServiceAndProductUseCase(
            gh<_i497.ServiceAndProductRepository>()));
    gh.factory<_i890.SaveServiceAndProductUseCase>(() =>
        _i890.SaveServiceAndProductUseCase(
            gh<_i497.ServiceAndProductRepository>()));
    gh.factory<_i891.ServicesGetServiceAndProductUseCase>(() =>
        _i891.ServicesGetServiceAndProductUseCase(
            gh<_i497.ServiceAndProductRepository>()));
    gh.factory<_i892.ProductsGetServiceAndProductUseCase>(() =>
        _i892.ProductsGetServiceAndProductUseCase(
            gh<_i497.ServiceAndProductRepository>()));
    gh.factory<_i893.RemoveServiceAndProductUseCase>(() =>
        _i893.RemoveServiceAndProductUseCase(
            gh<_i497.ServiceAndProductRepository>()));
    gh.factory<_i894.GetSavedServiceAndProductUseCase>(() =>
        _i894.GetSavedServiceAndProductUseCase(
            gh<_i497.ServiceAndProductRepository>()));
    gh.factory<_i895.GetCategoryServiceAndProductUseCase>(() =>
        _i895.GetCategoryServiceAndProductUseCase(
            gh<_i497.ServiceAndProductRepository>()));
    gh.factory<_i896.TypeApprovingBloc>(() => _i896.TypeApprovingBloc(
          gh<_i595.ApprovingSignalEformUseCase>(),
          gh<_i769.UploadUseCase>(),
        ));
    gh.factory<_i897.MedicalTemplateListBloc>(
        () => _i897.MedicalTemplateListBloc(
              gh<_i859.GetMedicalTemplateListUseCase>(),
              gh<_i861.SaveMedicalTemplateListUseCase>(),
              gh<_i860.GetSavedMedicalTemplateListUseCase>(),
              gh<_i857.RemoveMedicalTemplateListUseCase>(),
              gh<_i858.MedicalTemplateDetailGetMedicalTemplateListUseCase>(),
            ));
    gh.factory<_i898.ProductConfirmBloc>(() => _i898.ProductConfirmBloc(
          gh<_i585.GetProductConfirmUseCase>(),
          gh<_i582.GetProductConfirmBranchUseCase>(),
        ));
    gh.factory<_i899.SelectingStaffBloc>(
        () => _i899.SelectingStaffBloc(gh<_i593.GetStaffUseCase>()));
    gh.factory<_i900.FunctionRoomBloc>(
        () => _i900.FunctionRoomBloc(gh<_i592.GetFunctionRoomUseCase>()));
    gh.factory<_i901.ListFetchByStaffConsultationManagerUseCase>(() =>
        _i901.ListFetchByStaffConsultationManagerUseCase(
            gh<_i629.ConsultationManagerRepository>()));
    gh.factory<_i902.RoomFetchConsultationManagerUseCase>(() =>
        _i902.RoomFetchConsultationManagerUseCase(
            gh<_i629.ConsultationManagerRepository>()));
    gh.factory<_i903.GetConsultationManagerUseCase>(() =>
        _i903.GetConsultationManagerUseCase(
            gh<_i629.ConsultationManagerRepository>()));
    gh.factory<_i904.RemoveConsultationManagerUseCase>(() =>
        _i904.RemoveConsultationManagerUseCase(
            gh<_i629.ConsultationManagerRepository>()));
    gh.factory<_i905.SaveConsultationManagerUseCase>(() =>
        _i905.SaveConsultationManagerUseCase(
            gh<_i629.ConsultationManagerRepository>()));
    gh.factory<_i906.DeleteServiceAssignUseCase>(() =>
        _i906.DeleteServiceAssignUseCase(
            gh<_i629.ConsultationManagerRepository>()));
    gh.factory<_i907.AssignUpdateUseCase>(() =>
        _i907.AssignUpdateUseCase(gh<_i629.ConsultationManagerRepository>()));
    gh.factory<_i908.BedAssignConsultationManagerUseCase>(() =>
        _i908.BedAssignConsultationManagerUseCase(
            gh<_i629.ConsultationManagerRepository>()));
    gh.factory<_i909.GetSavedConsultationManagerUseCase>(() =>
        _i909.GetSavedConsultationManagerUseCase(
            gh<_i629.ConsultationManagerRepository>()));
    gh.factory<_i910.BedFetchConsultationManagerUseCase>(() =>
        _i910.BedFetchConsultationManagerUseCase(
            gh<_i629.ConsultationManagerRepository>()));
    gh.factory<_i911.DeleteServiceCustomerUseCase>(() =>
        _i911.DeleteServiceCustomerUseCase(
            gh<_i629.ConsultationManagerRepository>()));
    gh.factory<_i912.GetCustomerConsultationManagerUseCase>(() =>
        _i912.GetCustomerConsultationManagerUseCase(
            gh<_i629.ConsultationManagerRepository>()));
    gh.factory<_i913.SelectPxRoomBloc>(() => _i913.SelectPxRoomBloc(
          gh<_i817.GetRoomListCustomerUseCase>(),
          gh<_i422.SaveSelectPxRoomUseCase>(),
          gh<_i419.RemoveSelectPxRoomUseCase>(),
          gh<_i818.SaveCustomerRoomCodeUseCase>(),
          gh<_i420.RoomChangeSelectPxRoomUseCase>(),
        ));
    gh.factory<_i914.EmployeesInRoomUseCase>(() =>
        _i914.EmployeesInRoomUseCase(gh<_i547.PxUnasignedUpdateRepository>()));
    gh.factory<_i915.EmployeesFetchPxUnasignedUpdateUseCase>(() =>
        _i915.EmployeesFetchPxUnasignedUpdateUseCase(
            gh<_i547.PxUnasignedUpdateRepository>()));
    gh.factory<_i916.AssignPxUnasignedUpdateUseCase>(() =>
        _i916.AssignPxUnasignedUpdateUseCase(
            gh<_i547.PxUnasignedUpdateRepository>()));
    gh.factory<_i917.WorksFetchPxUnasignedUpdateUseCase>(() =>
        _i917.WorksFetchPxUnasignedUpdateUseCase(
            gh<_i547.PxUnasignedUpdateRepository>()));
    gh.factory<_i918.GetPxUnasignedUpdateUseCase>(() =>
        _i918.GetPxUnasignedUpdateUseCase(
            gh<_i547.PxUnasignedUpdateRepository>()));
    gh.factory<_i919.SavePxUnasignedUpdateUseCase>(() =>
        _i919.SavePxUnasignedUpdateUseCase(
            gh<_i547.PxUnasignedUpdateRepository>()));
    gh.factory<_i920.RemovePxUnasignedUpdateUseCase>(() =>
        _i920.RemovePxUnasignedUpdateUseCase(
            gh<_i547.PxUnasignedUpdateRepository>()));
    gh.factory<_i921.GetSavedPxUnasignedUpdateUseCase>(() =>
        _i921.GetSavedPxUnasignedUpdateUseCase(
            gh<_i547.PxUnasignedUpdateRepository>()));
    gh.factory<_i922.HomeBloc>(() => _i922.HomeBloc(
          gh<_i551.GetServicesUseCase>(),
          gh<_i814.CheckLeaderHomeUseCase>(),
          gh<_i815.WorkLeaderCheckHomeUseCase>(),
          gh<_i817.GetRoomListCustomerUseCase>(),
        ));
    gh.factory<_i923.MedicineDetailBloc>(() => _i923.MedicineDetailBloc(
          gh<_i709.GetMedicineDetailUseCase>(),
          gh<_i710.SaveMedicineDetailUseCase>(),
          gh<_i708.GetSavedMedicineDetailUseCase>(),
          gh<_i704.RemoveMedicineDetailUseCase>(),
          gh<_i705.GetUnitMedicineDetailUseCase>(),
        ));
    gh.factory<_i924.TicketBloc>(() => _i924.TicketBloc(
          gh<_i432.GetTicketTicketv2UseCase>(),
          gh<_i429.TicketCreatedTypeUseCase>(),
          gh<_i434.GetMyTicketTicketv2UseCase>(),
          gh<_i433.TicketUploadFileUseCase>(),
          gh<_i431.CreateTicketUseCase>(),
          gh<_i437.TicketGroupTypeUseCase>(),
          gh<_i435.UpdateTicketUseCase>(),
          gh<_i605.GetTicketActiveUseCase>(),
          gh<_i430.GetTicketAllGroupUseCase>(),
          gh<_i436.GetTicketAllTypeUseCase>(),
        ));
    gh.factory<_i925.OrderFoodServiceBloc>(() => _i925.OrderFoodServiceBloc(
          gh<_i320.OrderFoodUploadUseCase>(),
          gh<_i525.OrderFoodCreateReportUseCase>(),
        ));
    gh.factory<_i926.DetailNotificationBloc>(() =>
        _i926.DetailNotificationBloc(gh<_i633.GetDetailNotificationUseCase>()));
    gh.factory<_i927.GetListSupportRequestUseCase>(() =>
        _i927.GetListSupportRequestUseCase(gh<_i676.RequestRepository>()));
    gh.factory<_i928.SendSupportRequestUseCase>(
        () => _i928.SendSupportRequestUseCase(gh<_i676.RequestRepository>()));
    gh.factory<_i929.EditHomeMenuBloc>(
        () => _i929.EditHomeMenuBloc(gh<_i551.GetServicesUseCase>()));
    gh.factory<_i930.MedicalServiceLogListBloc>(
        () => _i930.MedicalServiceLogListBloc(
              gh<_i810.GetMedicalServiceLogListUseCase>(),
              gh<_i808.SaveMedicalServiceLogListUseCase>(),
              gh<_i811.GetSavedMedicalServiceLogListUseCase>(),
              gh<_i809.RemoveMedicalServiceLogListUseCase>(),
            ));
    gh.factory<_i931.UserTicketBloc>(() => _i931.UserTicketBloc(
          gh<_i443.GetUserTicketUseCase>(),
          gh<_i770.GetUserInfoUseCase>(),
        ));
    gh.factory<_i932.ProfileBloc>(() => _i932.ProfileBloc(
          gh<_i759.GetProfileUseCase>(),
          gh<_i652.UploadAvatarUseCase>(),
          gh<_i764.UpdateBioUseCase>(),
          gh<_i653.UploadBackgroundUseCase>(),
        ));
    gh.factory<_i933.CustomerListBloc>(() => _i933.CustomerListBloc(
          gh<_i739.GetCustomerListUseCase>(),
          gh<_i738.SaveCustomerListUseCase>(),
          gh<_i740.GetSavedCustomerListUseCase>(),
          gh<_i741.RemoveCustomerListUseCase>(),
          gh<_i742.GetCustomerRelationShipListUseCase>(),
        ));
    gh.factory<_i934.CreateCustomerBloc>(() => _i934.CreateCustomerBloc(
          gh<_i576.GetCreateCustomerUseCase>(),
          gh<_i573.SaveCreateCustomerUseCase>(),
          gh<_i577.GetSavedCreateCustomerUseCase>(),
          gh<_i572.RemoveCreateCustomerUseCase>(),
          gh<_i570.GetProvinceCreateCustomerUseCase>(),
          gh<_i574.GetDistrictCreateCustomerUseCase>(),
          gh<_i575.GetWardCreateCustomerUseCase>(),
          gh<_i571.GetJobCreateCustomerUseCase>(),
          gh<_i476.GetChatSelectBranchUseCase>(),
          gh<_i578.UpdateCreateCustomerUseCase>(),
          gh<_i579.SurveyLoadCreateCustomerUseCase>(),
          gh<_i580.CustomerSearchCreateCustomerUseCase>(),
        ));
    gh.factory<_i935.TicketDetailBloc>(() => _i935.TicketDetailBloc(
          gh<_i555.GetTicketDetailUseCase>(),
          gh<_i554.SaveTicketDetailUseCase>(),
          gh<_i557.GetSavedTicketDetailUseCase>(),
          gh<_i561.RemoveTicketDetailUseCase>(),
          gh<_i556.GetTicketDetailReasonUseCase>(),
          gh<_i560.ConfirmTicketDetailUseCase>(),
          gh<_i606.CreateTicketActiveUseCase>(),
          gh<_i558.ReceptTicketDetailUseCase>(),
          gh<_i433.TicketUploadFileUseCase>(),
          gh<_i605.GetTicketActiveUseCase>(),
          gh<_i559.TicketDetailReworkUseCase>(),
        ));
    gh.factory<_i936.SetPasswordBloc>(
        () => _i936.SetPasswordBloc(gh<_i760.ResetPasswordUseCase>()));
    gh.factory<_i937.CheckinPhotoBloc>(() => _i937.CheckinPhotoBloc(
          gh<_i665.GetCheckinPhotoUseCase>(),
          gh<_i667.SaveCheckinPhotoUseCase>(),
          gh<_i666.GetSavedCheckinPhotoUseCase>(),
          gh<_i668.RemoveCheckinPhotoUseCase>(),
        ));
    gh.factory<_i938.StoryWriteBloc>(() => _i938.StoryWriteBloc(
          gh<_i465.GetStoryRuleUseCase>(),
          gh<_i461.SocialUploadFileUseCase>(),
          gh<_i460.UpdateStoryUseCase>(),
          gh<_i609.GetLocationGoogleUseCase>(),
          gh<_i610.GetAddressAddressLocationGoogleUseCase>(),
          gh<_i608.GetAddressNearLocationGoogleUseCase>(),
          gh<_i611.GetAddressSearchLocationGoogleUseCase>(),
          gh<_i454.GetEmojiListUseCase>(),
        ));
    gh.factory<_i939.AuthenticationBloc>(() => _i939.AuthenticationBloc(
          gh<_i762.GetConfigurationUseCase>(),
          gh<_i776.HasTokenUseCase>(),
          gh<_i757.GetProfilesUseCase>(),
          gh<_i358.ClearCacheUseCase>(),
          gh<_i775.IsShowBoardingUseCase>(),
          gh<_i354.SetOnboardingUseCase>(),
          gh<_i658.CheckinPermissionCheckUserUseCase>(),
          gh<_i362.CheckinUseCase>(),
          gh<_i246.GetLatitudeUseCase>(),
          gh<_i249.GetLongitudeUseCase>(),
          gh<_i755.LogoutUseCase>(),
          gh<_i778.CacheUserUseCase>(),
          gh<_i651.UploadCheckInImageUseCase>(),
          gh<_i777.CacheLoginStringeeGetUseCase>(),
          gh<_i587.SocketAccessTokenGetLoginUseCase>(),
        ));
    gh.factory<_i940.CustomerScheduleBloc>(() => _i940.CustomerScheduleBloc(
          gh<_i714.GetCustomerScheduleUseCase>(),
          gh<_i711.SaveCustomerScheduleUseCase>(),
          gh<_i712.GetSavedCustomerScheduleUseCase>(),
          gh<_i713.RemoveCustomerScheduleUseCase>(),
        ));
    gh.factory<_i941.CustomerBloc>(() => _i941.CustomerBloc(
          gh<_i646.GetCustomerBookingInfoUseCase>(),
          gh<_i816.CheckinCustomerUseCase>(),
          gh<_i647.BookedServicesFetchCustomerBookingInfoUseCase>(),
          gh<_i817.GetRoomListCustomerUseCase>(),
          gh<_i819.PrintCustomerUseCase>(),
        ));
    gh.factory<_i942.ApprovingOtpBloc>(
        () => _i942.ApprovingOtpBloc(gh<_i596.ApprovingOtpEformUseCase>()));
    gh.factory<_i943.UserProfileBloc>(
        () => _i943.UserProfileBloc(gh<_i778.CacheUserUseCase>()));
    gh.factory<_i944.DeleteAssignTaskUseCase>(
        () => _i944.DeleteAssignTaskUseCase(gh<_i638.AssignTaskRepository>()));
    gh.factory<_i945.UpdateAssignTaskUseCase>(
        () => _i945.UpdateAssignTaskUseCase(gh<_i638.AssignTaskRepository>()));
    gh.factory<_i946.GetSavedAssignTaskUseCase>(() =>
        _i946.GetSavedAssignTaskUseCase(gh<_i638.AssignTaskRepository>()));
    gh.factory<_i947.CreateAssignTaskUseCase>(
        () => _i947.CreateAssignTaskUseCase(gh<_i638.AssignTaskRepository>()));
    gh.factory<_i948.SaveAssignTaskUseCase>(
        () => _i948.SaveAssignTaskUseCase(gh<_i638.AssignTaskRepository>()));
    gh.factory<_i949.GetAssignTaskUseCase>(
        () => _i949.GetAssignTaskUseCase(gh<_i638.AssignTaskRepository>()));
    gh.factory<_i950.RemoveAssignTaskUseCase>(
        () => _i950.RemoveAssignTaskUseCase(gh<_i638.AssignTaskRepository>()));
    gh.factory<_i951.GetStaffAssignTaskUseCase>(() =>
        _i951.GetStaffAssignTaskUseCase(gh<_i638.AssignTaskRepository>()));
    gh.factory<_i952.CustomerInfoDetailsBloc>(
        () => _i952.CustomerInfoDetailsBloc(
              gh<_i681.GetCustomerInfoDetailsUseCase>(),
              gh<_i680.SaveCustomerInfoDetailsUseCase>(),
              gh<_i683.GetSavedCustomerInfoDetailsUseCase>(),
              gh<_i684.RemoveCustomerInfoDetailsUseCase>(),
              gh<_i682.CheckoutCustomerInfoDetailsUseCase>(),
            ));
    gh.factory<_i953.LoginBloc>(() => _i953.LoginBloc(
          gh<_i752.GetOtpUseCase>(),
          gh<_i355.HasUserDataUseCase>(),
          gh<_i753.CheckPhoneUseCase>(),
          gh<_i754.LoginUseCase>(),
          gh<_i778.CacheUserUseCase>(),
          gh<_i765.LoginSocialUseCase>(),
          gh<_i587.SocketAccessTokenGetLoginUseCase>(),
          gh<_i660.CheckPermissionUserUseCase>(),
        ));
    gh.factory<_i954.TagImageBloc>(() => _i954.TagImageBloc(
          gh<_i817.GetRoomListCustomerUseCase>(),
          gh<_i823.GetComboTagUsecase>(),
          gh<_i824.GetImageByComboTagUsecae>(),
        ));
    gh.factory<_i955.MonthlyHistoryCheckinBloc>(() =>
        _i955.MonthlyHistoryCheckinBloc(
            gh<_i842.GetMonthlyHistoryCheckinUseCase>()));
    gh.factory<_i956.ImageByComboTagBloc>(
        () => _i956.ImageByComboTagBloc(gh<_i824.GetImageByComboTagUsecae>()));
    gh.factory<_i957.ConsultationHistoryBloc>(() =>
        _i957.ConsultationHistoryBloc(
            gh<_i695.GetConsultationHistoryCustomerProfileUseCase>()));
    gh.factory<_i958.CustomerProfileBloc>(() => _i958.CustomerProfileBloc(
          gh<_i696.GetCustomerProfileUseCase>(),
          gh<_i693.SaveCustomerProfileUseCase>(),
          gh<_i697.GetSavedCustomerProfileUseCase>(),
          gh<_i694.RemoveCustomerProfileUseCase>(),
        ));
    gh.factory<_i959.CustomerBookingInfoBloc>(
        () => _i959.CustomerBookingInfoBloc(
              gh<_i646.GetCustomerBookingInfoUseCase>(),
              gh<_i645.SaveCustomerBookingInfoUseCase>(),
              gh<_i648.GetSavedCustomerBookingInfoUseCase>(),
              gh<_i643.RemoveCustomerBookingInfoUseCase>(),
              gh<_i647.BookedServicesFetchCustomerBookingInfoUseCase>(),
              gh<_i642.SuggestServicesFetchCustomerBookingInfoUseCase>(),
              gh<_i644.ServiceDetailsLoadCustomerBookingInfoUseCase>(),
              gh<_i817.GetRoomListCustomerUseCase>(),
              gh<_i293.GetServiceInsideTicketUseCase>(),
            ));
    gh.factory<_i960.RatingHumanBloc>(() => _i960.RatingHumanBloc(
          gh<_i702.GetRatingHumanUseCase>(),
          gh<_i700.GetQuestionDetailUseCase>(),
          gh<_i701.SaveRatingHumanUseCase>(),
          gh<_i699.SubmitRatingHumanUseCase>(),
        ));
    gh.factory<_i961.ConsultationHistoryDetailBloc>(
        () => _i961.ConsultationHistoryDetailBloc(
              gh<_i692.CreateConsultationCustomerProfileUseCase>(),
              gh<_i698.UpdateConsultationCustomerProfileUseCase>(),
              gh<_i617.EmployeeGetBranchSelectionUseCase>(),
            ));
    gh.factory<_i962.GeneralBloc>(() => _i962.GeneralBloc(
          gh<_i802.UploadRecordTakingCareCustomerUseCase>(),
          gh<_i778.CacheUserUseCase>(),
          gh<_i650.UploadFeedbackUseCase>(),
          gh<_i778.CacheUserUseCase>(),
          gh<_i727.SendFeedbackUseCase>(),
          gh<_i772.CacheQuickActionGetUseCase>(),
          gh<_i771.CacheQuickActionRemoveUseCase>(),
          gh<_i160.GetConversationByInviteIdChatListUseCase>(),
          gh<_i153.JoinGroupChatListUseCase>(),
          gh<_i367.UniversalQrScanUseCase>(),
        ));
    gh.factory<_i963.PxUnasignedBloc>(() => _i963.PxUnasignedBloc(
          gh<_i744.GetPxCustomerListUseCase>(),
          gh<_i745.SavePxUnasignedUseCase>(),
          gh<_i746.GetSavedPxUnasignedUseCase>(),
          gh<_i743.RemovePxUnasignedUseCase>(),
        ));
    gh.factory<_i964.DetailCrmCustomerBloc>(() => _i964.DetailCrmCustomerBloc(
          gh<_i778.CacheUserUseCase>(),
          gh<_i531.GetDetailCrmCustomerUseCase>(),
          gh<_i532.SaveDetailCrmCustomerUseCase>(),
          gh<_i537.GetSavedDetailCrmCustomerUseCase>(),
          gh<_i534.RemoveDetailCrmCustomerUseCase>(),
          gh<_i530.AdviceFetchDetailCrmCustomerUseCase>(),
          gh<_i529.ServiceFetchDetailCrmCustomerUseCase>(),
          gh<_i535.CallLogFetchDetailCrmCustomerUseCase>(),
          gh<_i544.MessageLogFetchDetailCrmCustomerUseCase>(),
          gh<_i538.BookingLogFetchDetailCrmCustomerUseCase>(),
          gh<_i542.AdviceTypeFetchDetailCrmCustomerUseCase>(),
          gh<_i541.AdviceUpdateDetailCrmCustomerUseCase>(),
          gh<_i536.BranchLoadDetailCrmCustomerUseCase>(),
          gh<_i528.PromotionLoadDetailCrmCustomerUseCase>(),
          gh<_i527.RoomLoadDetailCrmCustomerUseCase>(),
          gh<_i540.TimeLoadDetailCrmCustomerUseCase>(),
          gh<_i533.ServiceLoadDetailCrmCustomerUseCase>(),
          gh<_i545.NumberBookingLoadDetailCrmCustomerUseCase>(),
          gh<_i543.BookingDetailLoadDetailCrmCustomerUseCase>(),
          gh<_i539.BookDetailCrmCustomerUseCase>(),
          gh<_i546.BookingLoadDetailCrmCustomerUseCase>(),
        ));
    gh.factory<_i965.FeedbackBloc>(() => _i965.FeedbackBloc(
          gh<_i766.SubmitFeedbackUseCase>(),
          gh<_i769.UploadUseCase>(),
          gh<_i778.CacheUserUseCase>(),
        ));
    gh.factory<_i966.PxUnasignedUpdateBloc>(() => _i966.PxUnasignedUpdateBloc(
          gh<_i918.GetPxUnasignedUpdateUseCase>(),
          gh<_i919.SavePxUnasignedUpdateUseCase>(),
          gh<_i921.GetSavedPxUnasignedUpdateUseCase>(),
          gh<_i920.RemovePxUnasignedUpdateUseCase>(),
          gh<_i917.WorksFetchPxUnasignedUpdateUseCase>(),
          gh<_i915.EmployeesFetchPxUnasignedUpdateUseCase>(),
          gh<_i916.AssignPxUnasignedUpdateUseCase>(),
          gh<_i793.GetSectionTakingCareCustomerUseCase>(),
        ));
    gh.factory<_i967.MedicalProductCreationBloc>(
        () => _i967.MedicalProductCreationBloc(
              gh<_i512.MedicalProductCreationUseCase>(),
              gh<_i511.SaveMedicalProductCreationUseCase>(),
              gh<_i509.GetSavedMedicalProductCreationUseCase>(),
              gh<_i510.RemoveMedicalProductCreationUseCase>(),
              gh<_i508.ProductsMedicalProductCreationUseCase>(),
              gh<_i888.GetServiceAndProductActionsUseCase>(),
            ));
    gh.factory<_i968.ServiceDetailBloc>(() => _i968.ServiceDetailBloc(
          gh<_i793.GetSectionTakingCareCustomerUseCase>(),
          gh<_i319.GetTreatmentDetailUseCase>(),
          gh<_i299.CreateTreatmentDetailUseCase>(),
          gh<_i656.EmployeeFetchServiceDetailUseCase>(),
          gh<_i655.DoctorFetchServiceDetailUseCase>(),
          gh<_i304.GetTreatmentOMDetailUseCase>(),
          gh<_i291.CreateTreatmentOMDetailUseCase>(),
          gh<_i914.EmployeesInRoomUseCase>(),
          gh<_i301.GetTreatmentNoteUseCase>(),
          gh<_i311.UpdateTreatmentNoteUseCase>(),
          gh<_i305.GetResultOfFitUseCase>(),
          gh<_i313.UpdateResultOfFitUseCase>(),
          gh<_i292.DeleteResultOfFitUseCase>(),
          gh<_i918.GetPxUnasignedUpdateUseCase>(),
          gh<_i317.UpdateTreatmentDetailUseCase>(),
          gh<_i302.GetServiceUsageConsultationCustomerUseCase>(),
        ));
    gh.factory<_i969.ActionAttendanceBloc>(() => _i969.ActionAttendanceBloc(
          gh<_i841.GetCheckinTypesUseCase>(),
          gh<_i839.RequestUpdateHistoryCheckinUseCase>(),
          gh<_i840.GetBranchesUseCase>(),
          gh<_i476.GetChatSelectBranchUseCase>(),
        ));
    gh.factory<_i970.PxTaskListBloc>(() => _i970.PxTaskListBloc(
          gh<_i744.GetPxCustomerListUseCase>(),
          gh<_i519.SavePxTaskListUseCase>(),
          gh<_i518.GetSavedPxTaskListUseCase>(),
          gh<_i517.RemovePxTaskListUseCase>(),
          gh<_i793.GetSectionTakingCareCustomerUseCase>(),
          gh<_i881.AssignsFetchPxRecheckUseCase>(),
        ));
    gh.factory<_i971.CreateSupportRequestsBloc>(() =>
        _i971.CreateSupportRequestsBloc(gh<_i928.SendSupportRequestUseCase>()));
    gh.factory<_i972.MedicalLogDetailBloc>(() => _i972.MedicalLogDetailBloc(
          gh<_i875.GetMedicalLogDetailUseCase>(),
          gh<_i877.SaveMedicalLogDetailUseCase>(),
          gh<_i872.GetSavedMedicalLogDetailUseCase>(),
          gh<_i868.RemoveMedicalLogDetailUseCase>(),
          gh<_i873.CreateLogMedicalDetailUseCase>(),
          gh<_i867.UpdateLogMedicalDetailUseCase>(),
          gh<_i874.GetSkinMachineMedicalLogDetailUseCase>(),
          gh<_i876.GetPostSaiMedicalLogDetailUseCase>(),
          gh<_i769.UploadUseCase>(),
          gh<_i866.MedicineListGetMedicalLogDetailUseCase>(),
          gh<_i865.DosageListGetMedicalLogDetailUseCase>(),
          gh<_i862.HaPointListGetMedicalLogDetailUseCase>(),
          gh<_i870.KhacnhoListGetMedicalLogDetailUseCase>(),
          gh<_i617.EmployeeGetBranchSelectionUseCase>(),
          gh<_i863.DoctorListGetMedicalLogDetailUseCase>(),
          gh<_i871.GetOriginStatusMedicalLogDetailUseCase>(),
          gh<_i864.GetTattooColorMedicalLogDetailUseCase>(),
          gh<_i869.GetTattooTimeMedicalLogDetailUseCase>(),
          gh<_i706.CreateMedicineDetailUseCase>(),
          gh<_i707.UpdateMedicineDetailUseCase>(),
        ));
    gh.factory<_i973.SettingBloc>(() => _i973.SettingBloc(
          gh<_i661.UserDeletionUseCase>(),
          gh<_i768.ChangePasswordUseCase>(),
          gh<_i654.UploadKYCUseCase>(),
          gh<_i657.SendKycPhotosSettingUseCase>(),
        ));
    gh.factory<_i974.ConsultationCustomerBloc>(
        () => _i974.ConsultationCustomerBloc(
              gh<_i308.GetConsultationCustomerUseCase>(),
              gh<_i310.SaveConsultationCustomerUseCase>(),
              gh<_i306.GetSavedConsultationCustomerUseCase>(),
              gh<_i297.RemoveConsultationCustomerUseCase>(),
              gh<_i303.GetServiceConsultationCustomerUseCase>(),
              gh<_i312.GetActionConsultationCustomerUseCase>(),
              gh<_i315.CompleteConsultationCustomerUseCase>(),
              gh<_i298.ProductLoadConsultationCustomerUseCase>(),
              gh<_i291.CreateTreatmentOMDetailUseCase>(),
              gh<_i817.GetRoomListCustomerUseCase>(),
              gh<_i300.GetFitCustomerInfoUseCase>(),
              gh<_i309.UpdateFitCustomerInfoUseCase>(),
              gh<_i295.GetResultListOfFitUseCase>(),
              gh<_i293.GetServiceInsideTicketUseCase>(),
              gh<_i307.UpdateSkinCustomerInfoUseCase>(),
              gh<_i296.GetSkinCustomerInfoUseCase>(),
              gh<_i318.RemoveServiceConsultationCustomerUseCase>(),
              gh<_i316.EditServiceConsultationCustomerUseCase>(),
              gh<_i314.UpdateConsultationTTBDUseCase>(),
              gh<_i294.GetConsultationNDTVUseCase>(),
            ));
    gh.factory<_i975.MedicalServiceCreationBloc>(
        () => _i975.MedicalServiceCreationBloc(
              gh<_i725.MedicalServiceCreationUseCase>(),
              gh<_i724.SaveMedicalServiceCreationUseCase>(),
              gh<_i723.GetSavedMedicalServiceCreationUseCase>(),
              gh<_i720.RemoveMedicalServiceCreationUseCase>(),
              gh<_i721.ServicesMedicalServiceCreationUseCase>(),
              gh<_i722.MethodsMedicalServiceCreationUseCase>(),
              gh<_i888.GetServiceAndProductActionsUseCase>(),
            ));
    gh.factory<_i976.MoreBloc>(() => _i976.MoreBloc(
          gh<_i650.UploadFeedbackUseCase>(),
          gh<_i757.GetProfilesUseCase>(),
          gh<_i727.SendFeedbackUseCase>(),
        ));
    gh.factory<_i977.PxRecheckBloc>(() => _i977.PxRecheckBloc(
          gh<_i744.GetPxCustomerListUseCase>(),
          gh<_i745.SavePxUnasignedUseCase>(),
          gh<_i746.GetSavedPxUnasignedUseCase>(),
          gh<_i743.RemovePxUnasignedUseCase>(),
          gh<_i881.AssignsFetchPxRecheckUseCase>(),
          gh<_i879.NoteFinishPxRecheckUseCase>(),
          gh<_i915.EmployeesFetchPxUnasignedUpdateUseCase>(),
          gh<_i847.AssignPxRecheckUpdateUseCase>(),
          gh<_i880.WorkStatusUpdatePxRecheckUseCase>(),
        ));
    gh.factory<_i978.PxListBloc>(() => _i978.PxListBloc(
          gh<_i806.SavePxListUseCase>(),
          gh<_i804.GetSavedPxListUseCase>(),
          gh<_i807.RemovePxListUseCase>(),
        ));
    gh.factory<_i979.HistoryCheckinBloc>(() => _i979.HistoryCheckinBloc(
          gh<_i839.RequestUpdateHistoryCheckinUseCase>(),
          gh<_i482.FetchHistoryCheckinUseCase>(),
        ));
    gh.factory<_i980.SupportRequestsBloc>(() =>
        _i980.SupportRequestsBloc(gh<_i927.GetListSupportRequestUseCase>()));
    gh.factory<_i981.AssignTaskBloc>(() => _i981.AssignTaskBloc(
          gh<_i949.GetAssignTaskUseCase>(),
          gh<_i948.SaveAssignTaskUseCase>(),
          gh<_i946.GetSavedAssignTaskUseCase>(),
          gh<_i950.RemoveAssignTaskUseCase>(),
          gh<_i951.GetStaffAssignTaskUseCase>(),
          gh<_i947.CreateAssignTaskUseCase>(),
          gh<_i944.DeleteAssignTaskUseCase>(),
          gh<_i945.UpdateAssignTaskUseCase>(),
        ));
    gh.factory<_i982.ServiceAndProductBloc>(() => _i982.ServiceAndProductBloc(
          gh<_i889.GetServiceAndProductUseCase>(),
          gh<_i890.SaveServiceAndProductUseCase>(),
          gh<_i894.GetSavedServiceAndProductUseCase>(),
          gh<_i893.RemoveServiceAndProductUseCase>(),
          gh<_i895.GetCategoryServiceAndProductUseCase>(),
          gh<_i891.ServicesGetServiceAndProductUseCase>(),
          gh<_i892.ProductsGetServiceAndProductUseCase>(),
        ));
    gh.factory<_i983.ConsultationManagerBloc>(
        () => _i983.ConsultationManagerBloc(
              gh<_i903.GetConsultationManagerUseCase>(),
              gh<_i778.CacheUserUseCase>(),
              gh<_i805.GetPxListUseCase>(),
              gh<_i905.SaveConsultationManagerUseCase>(),
              gh<_i909.GetSavedConsultationManagerUseCase>(),
              gh<_i904.RemoveConsultationManagerUseCase>(),
              gh<_i912.GetCustomerConsultationManagerUseCase>(),
              gh<_i817.GetRoomListCustomerUseCase>(),
              gh<_i420.RoomChangeSelectPxRoomUseCase>(),
              gh<_i910.BedFetchConsultationManagerUseCase>(),
              gh<_i908.BedAssignConsultationManagerUseCase>(),
              gh<_i915.EmployeesFetchPxUnasignedUpdateUseCase>(),
              gh<_i907.AssignUpdateUseCase>(),
              gh<_i901.ListFetchByStaffConsultationManagerUseCase>(),
              gh<_i906.DeleteServiceAssignUseCase>(),
              gh<_i911.DeleteServiceCustomerUseCase>(),
            ));
    return this;
  }
}

class _$RegisterModule extends _i984.RegisterModule {}
